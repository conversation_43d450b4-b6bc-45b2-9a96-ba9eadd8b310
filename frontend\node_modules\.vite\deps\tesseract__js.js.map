{"version": 3, "sources": ["../../regenerator-runtime/runtime.js", "../../tesseract.js/src/utils/getId.js", "../../tesseract.js/src/createJob.js", "../../tesseract.js/src/utils/log.js", "../../tesseract.js/src/createScheduler.js", "../../is-electron/index.js", "../../tesseract.js/src/utils/getEnvironment.js", "../../tesseract.js/src/utils/resolvePaths.js", "../../tesseract.js/src/utils/circularize.js", "../../tesseract.js/src/constants/OEM.js", "../../tesseract.js/package.json", "../../tesseract.js/src/constants/defaultOptions.js", "../../tesseract.js/src/worker/browser/defaultOptions.js", "../../tesseract.js/src/worker/browser/spawnWorker.js", "../../tesseract.js/src/worker/browser/terminateWorker.js", "../../tesseract.js/src/worker/browser/onMessage.js", "../../tesseract.js/src/worker/browser/send.js", "../../tesseract.js/src/worker/browser/loadImage.js", "../../tesseract.js/src/worker/browser/index.js", "../../tesseract.js/src/createWorker.js", "../../tesseract.js/src/Tesseract.js", "../../tesseract.js/src/constants/languages.js", "../../tesseract.js/src/constants/PSM.js", "../../tesseract.js/src/index.js"], "sourcesContent": ["/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar runtime = (function (exports) {\n  \"use strict\";\n\n  var Op = Object.prototype;\n  var hasOwn = Op.hasOwnProperty;\n  var defineProperty = Object.defineProperty || function (obj, key, desc) { obj[key] = desc.value; };\n  var undefined; // More compressible than void 0.\n  var $Symbol = typeof Symbol === \"function\" ? Symbol : {};\n  var iteratorSymbol = $Symbol.iterator || \"@@iterator\";\n  var asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\";\n  var toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n\n  function define(obj, key, value) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n    return obj[key];\n  }\n  try {\n    // IE 8 has a broken Object.defineProperty that only works on DOM objects.\n    define({}, \"\");\n  } catch (err) {\n    define = function(obj, key, value) {\n      return obj[key] = value;\n    };\n  }\n\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;\n    var generator = Object.create(protoGenerator.prototype);\n    var context = new Context(tryLocsList || []);\n\n    // The ._invoke method unifies the implementations of the .next,\n    // .throw, and .return methods.\n    defineProperty(generator, \"_invoke\", { value: makeInvokeMethod(innerFn, self, context) });\n\n    return generator;\n  }\n  exports.wrap = wrap;\n\n  // Try/catch helper to minimize deoptimizations. Returns a completion\n  // record like context.tryEntries[i].completion. This interface could\n  // have been (and was previously) designed to take a closure to be\n  // invoked without arguments, but in all the cases we care about we\n  // already have an existing method we want to call, so there's no need\n  // to create a new function object. We can even get away with assuming\n  // the method takes exactly one argument, since that happens to be true\n  // in every case, so we don't have to touch the arguments object. The\n  // only additional allocation required is the completion record, which\n  // has a stable shape and so hopefully should be cheap to allocate.\n  function tryCatch(fn, obj, arg) {\n    try {\n      return { type: \"normal\", arg: fn.call(obj, arg) };\n    } catch (err) {\n      return { type: \"throw\", arg: err };\n    }\n  }\n\n  var GenStateSuspendedStart = \"suspendedStart\";\n  var GenStateSuspendedYield = \"suspendedYield\";\n  var GenStateExecuting = \"executing\";\n  var GenStateCompleted = \"completed\";\n\n  // Returning this object from the innerFn has the same effect as\n  // breaking out of the dispatch switch statement.\n  var ContinueSentinel = {};\n\n  // Dummy constructor functions that we use as the .constructor and\n  // .constructor.prototype properties for functions that return Generator\n  // objects. For full spec compliance, you may wish to configure your\n  // minifier not to mangle the names of these two functions.\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n\n  // This is a polyfill for %IteratorPrototype% for environments that\n  // don't natively support it.\n  var IteratorPrototype = {};\n  define(IteratorPrototype, iteratorSymbol, function () {\n    return this;\n  });\n\n  var getProto = Object.getPrototypeOf;\n  var NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  if (NativeIteratorPrototype &&\n      NativeIteratorPrototype !== Op &&\n      hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {\n    // This environment has a native %IteratorPrototype%; use it instead\n    // of the polyfill.\n    IteratorPrototype = NativeIteratorPrototype;\n  }\n\n  var Gp = GeneratorFunctionPrototype.prototype =\n    Generator.prototype = Object.create(IteratorPrototype);\n  GeneratorFunction.prototype = GeneratorFunctionPrototype;\n  defineProperty(Gp, \"constructor\", { value: GeneratorFunctionPrototype, configurable: true });\n  defineProperty(\n    GeneratorFunctionPrototype,\n    \"constructor\",\n    { value: GeneratorFunction, configurable: true }\n  );\n  GeneratorFunction.displayName = define(\n    GeneratorFunctionPrototype,\n    toStringTagSymbol,\n    \"GeneratorFunction\"\n  );\n\n  // Helper for defining the .next, .throw, and .return methods of the\n  // Iterator interface in terms of a single ._invoke method.\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function(method) {\n      define(prototype, method, function(arg) {\n        return this._invoke(method, arg);\n      });\n    });\n  }\n\n  exports.isGeneratorFunction = function(genFun) {\n    var ctor = typeof genFun === \"function\" && genFun.constructor;\n    return ctor\n      ? ctor === GeneratorFunction ||\n        // For the native GeneratorFunction constructor, the best we can\n        // do is to check its .name property.\n        (ctor.displayName || ctor.name) === \"GeneratorFunction\"\n      : false;\n  };\n\n  exports.mark = function(genFun) {\n    if (Object.setPrototypeOf) {\n      Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);\n    } else {\n      genFun.__proto__ = GeneratorFunctionPrototype;\n      define(genFun, toStringTagSymbol, \"GeneratorFunction\");\n    }\n    genFun.prototype = Object.create(Gp);\n    return genFun;\n  };\n\n  // Within the body of any async function, `await x` is transformed to\n  // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test\n  // `hasOwn.call(value, \"__await\")` to determine if the yielded value is\n  // meant to be awaited.\n  exports.awrap = function(arg) {\n    return { __await: arg };\n  };\n\n  function AsyncIterator(generator, PromiseImpl) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (record.type === \"throw\") {\n        reject(record.arg);\n      } else {\n        var result = record.arg;\n        var value = result.value;\n        if (value &&\n            typeof value === \"object\" &&\n            hasOwn.call(value, \"__await\")) {\n          return PromiseImpl.resolve(value.__await).then(function(value) {\n            invoke(\"next\", value, resolve, reject);\n          }, function(err) {\n            invoke(\"throw\", err, resolve, reject);\n          });\n        }\n\n        return PromiseImpl.resolve(value).then(function(unwrapped) {\n          // When a yielded Promise is resolved, its final value becomes\n          // the .value of the Promise<{value,done}> result for the\n          // current iteration.\n          result.value = unwrapped;\n          resolve(result);\n        }, function(error) {\n          // If a rejected Promise was yielded, throw the rejection back\n          // into the async generator function so it can be handled there.\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n    }\n\n    var previousPromise;\n\n    function enqueue(method, arg) {\n      function callInvokeWithMethodAndArg() {\n        return new PromiseImpl(function(resolve, reject) {\n          invoke(method, arg, resolve, reject);\n        });\n      }\n\n      return previousPromise =\n        // If enqueue has been called before, then we want to wait until\n        // all previous Promises have been resolved before calling invoke,\n        // so that results are always delivered in the correct order. If\n        // enqueue has not been called before, then it is important to\n        // call invoke immediately, without waiting on a callback to fire,\n        // so that the async generator function has the opportunity to do\n        // any necessary setup in a predictable way. This predictability\n        // is why the Promise constructor synchronously invokes its\n        // executor callback, and why async functions synchronously\n        // execute code before the first await. Since we implement simple\n        // async functions in terms of async generators, it is especially\n        // important to get this right, even though it requires care.\n        previousPromise ? previousPromise.then(\n          callInvokeWithMethodAndArg,\n          // Avoid propagating failures to Promises returned by later\n          // invocations of the iterator.\n          callInvokeWithMethodAndArg\n        ) : callInvokeWithMethodAndArg();\n    }\n\n    // Define the unified helper method that is used to implement .next,\n    // .throw, and .return (see defineIteratorMethods).\n    defineProperty(this, \"_invoke\", { value: enqueue });\n  }\n\n  defineIteratorMethods(AsyncIterator.prototype);\n  define(AsyncIterator.prototype, asyncIteratorSymbol, function () {\n    return this;\n  });\n  exports.AsyncIterator = AsyncIterator;\n\n  // Note that simple async functions are implemented on top of\n  // AsyncIterator objects; they just return a Promise for the value of\n  // the final result produced by the iterator.\n  exports.async = function(innerFn, outerFn, self, tryLocsList, PromiseImpl) {\n    if (PromiseImpl === void 0) PromiseImpl = Promise;\n\n    var iter = new AsyncIterator(\n      wrap(innerFn, outerFn, self, tryLocsList),\n      PromiseImpl\n    );\n\n    return exports.isGeneratorFunction(outerFn)\n      ? iter // If outerFn is a generator, return the full iterator.\n      : iter.next().then(function(result) {\n          return result.done ? result.value : iter.next();\n        });\n  };\n\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = GenStateSuspendedStart;\n\n    return function invoke(method, arg) {\n      if (state === GenStateExecuting) {\n        throw new Error(\"Generator is already running\");\n      }\n\n      if (state === GenStateCompleted) {\n        if (method === \"throw\") {\n          throw arg;\n        }\n\n        // Be forgiving, per 25.3.3.3.3 of the spec:\n        // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume\n        return doneResult();\n      }\n\n      context.method = method;\n      context.arg = arg;\n\n      while (true) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n\n        if (context.method === \"next\") {\n          // Setting context._sent for legacy support of Babel's\n          // function.sent implementation.\n          context.sent = context._sent = context.arg;\n\n        } else if (context.method === \"throw\") {\n          if (state === GenStateSuspendedStart) {\n            state = GenStateCompleted;\n            throw context.arg;\n          }\n\n          context.dispatchException(context.arg);\n\n        } else if (context.method === \"return\") {\n          context.abrupt(\"return\", context.arg);\n        }\n\n        state = GenStateExecuting;\n\n        var record = tryCatch(innerFn, self, context);\n        if (record.type === \"normal\") {\n          // If an exception is thrown from innerFn, we leave state ===\n          // GenStateExecuting and loop back for another invocation.\n          state = context.done\n            ? GenStateCompleted\n            : GenStateSuspendedYield;\n\n          if (record.arg === ContinueSentinel) {\n            continue;\n          }\n\n          return {\n            value: record.arg,\n            done: context.done\n          };\n\n        } else if (record.type === \"throw\") {\n          state = GenStateCompleted;\n          // Dispatch the exception by looping back around to the\n          // context.dispatchException(context.arg) call above.\n          context.method = \"throw\";\n          context.arg = record.arg;\n        }\n      }\n    };\n  }\n\n  // Call delegate.iterator[context.method](context.arg) and handle the\n  // result, either by returning a { value, done } result from the\n  // delegate iterator, or by modifying context.method and context.arg,\n  // setting context.delegate to null, and returning the ContinueSentinel.\n  function maybeInvokeDelegate(delegate, context) {\n    var methodName = context.method;\n    var method = delegate.iterator[methodName];\n    if (method === undefined) {\n      // A .throw or .return when the delegate iterator has no .throw\n      // method, or a missing .next mehtod, always terminate the\n      // yield* loop.\n      context.delegate = null;\n\n      // Note: [\"return\"] must be used for ES3 parsing compatibility.\n      if (methodName === \"throw\" && delegate.iterator[\"return\"]) {\n        // If the delegate iterator has a return method, give it a\n        // chance to clean up.\n        context.method = \"return\";\n        context.arg = undefined;\n        maybeInvokeDelegate(delegate, context);\n\n        if (context.method === \"throw\") {\n          // If maybeInvokeDelegate(context) changed context.method from\n          // \"return\" to \"throw\", let that override the TypeError below.\n          return ContinueSentinel;\n        }\n      }\n      if (methodName !== \"return\") {\n        context.method = \"throw\";\n        context.arg = new TypeError(\n          \"The iterator does not provide a '\" + methodName + \"' method\");\n      }\n\n      return ContinueSentinel;\n    }\n\n    var record = tryCatch(method, delegate.iterator, context.arg);\n\n    if (record.type === \"throw\") {\n      context.method = \"throw\";\n      context.arg = record.arg;\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    var info = record.arg;\n\n    if (! info) {\n      context.method = \"throw\";\n      context.arg = new TypeError(\"iterator result is not an object\");\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    if (info.done) {\n      // Assign the result of the finished delegate to the temporary\n      // variable specified by delegate.resultName (see delegateYield).\n      context[delegate.resultName] = info.value;\n\n      // Resume execution at the desired location (see delegateYield).\n      context.next = delegate.nextLoc;\n\n      // If context.method was \"throw\" but the delegate handled the\n      // exception, let the outer generator proceed normally. If\n      // context.method was \"next\", forget context.arg since it has been\n      // \"consumed\" by the delegate iterator. If context.method was\n      // \"return\", allow the original .return call to continue in the\n      // outer generator.\n      if (context.method !== \"return\") {\n        context.method = \"next\";\n        context.arg = undefined;\n      }\n\n    } else {\n      // Re-yield the result returned by the delegate method.\n      return info;\n    }\n\n    // The delegate iterator is finished, so forget it and continue with\n    // the outer generator.\n    context.delegate = null;\n    return ContinueSentinel;\n  }\n\n  // Define Generator.prototype.{next,throw,return} in terms of the\n  // unified ._invoke helper method.\n  defineIteratorMethods(Gp);\n\n  define(Gp, toStringTagSymbol, \"Generator\");\n\n  // A Generator should always return itself as the iterator object when the\n  // @@iterator function is called on it. Some browsers' implementations of the\n  // iterator prototype chain incorrectly implement this, causing the Generator\n  // object to not be returned from this call. This ensures that doesn't happen.\n  // See https://github.com/facebook/regenerator/issues/274 for more details.\n  define(Gp, iteratorSymbol, function() {\n    return this;\n  });\n\n  define(Gp, \"toString\", function() {\n    return \"[object Generator]\";\n  });\n\n  function pushTryEntry(locs) {\n    var entry = { tryLoc: locs[0] };\n\n    if (1 in locs) {\n      entry.catchLoc = locs[1];\n    }\n\n    if (2 in locs) {\n      entry.finallyLoc = locs[2];\n      entry.afterLoc = locs[3];\n    }\n\n    this.tryEntries.push(entry);\n  }\n\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\";\n    delete record.arg;\n    entry.completion = record;\n  }\n\n  function Context(tryLocsList) {\n    // The root entry object (effectively a try statement without a catch\n    // or a finally block) gives us a place to store values thrown from\n    // locations where there is no enclosing try statement.\n    this.tryEntries = [{ tryLoc: \"root\" }];\n    tryLocsList.forEach(pushTryEntry, this);\n    this.reset(true);\n  }\n\n  exports.keys = function(val) {\n    var object = Object(val);\n    var keys = [];\n    for (var key in object) {\n      keys.push(key);\n    }\n    keys.reverse();\n\n    // Rather than returning an object with a next method, we keep\n    // things simple and return the next function itself.\n    return function next() {\n      while (keys.length) {\n        var key = keys.pop();\n        if (key in object) {\n          next.value = key;\n          next.done = false;\n          return next;\n        }\n      }\n\n      // To avoid creating an additional object, we just hang the .value\n      // and .done properties off the next function object itself. This\n      // also ensures that the minifier will not anonymize the function.\n      next.done = true;\n      return next;\n    };\n  };\n\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) {\n        return iteratorMethod.call(iterable);\n      }\n\n      if (typeof iterable.next === \"function\") {\n        return iterable;\n      }\n\n      if (!isNaN(iterable.length)) {\n        var i = -1, next = function next() {\n          while (++i < iterable.length) {\n            if (hasOwn.call(iterable, i)) {\n              next.value = iterable[i];\n              next.done = false;\n              return next;\n            }\n          }\n\n          next.value = undefined;\n          next.done = true;\n\n          return next;\n        };\n\n        return next.next = next;\n      }\n    }\n\n    // Return an iterator with no values.\n    return { next: doneResult };\n  }\n  exports.values = values;\n\n  function doneResult() {\n    return { value: undefined, done: true };\n  }\n\n  Context.prototype = {\n    constructor: Context,\n\n    reset: function(skipTempReset) {\n      this.prev = 0;\n      this.next = 0;\n      // Resetting context._sent for legacy support of Babel's\n      // function.sent implementation.\n      this.sent = this._sent = undefined;\n      this.done = false;\n      this.delegate = null;\n\n      this.method = \"next\";\n      this.arg = undefined;\n\n      this.tryEntries.forEach(resetTryEntry);\n\n      if (!skipTempReset) {\n        for (var name in this) {\n          // Not sure about the optimal order of these conditions:\n          if (name.charAt(0) === \"t\" &&\n              hasOwn.call(this, name) &&\n              !isNaN(+name.slice(1))) {\n            this[name] = undefined;\n          }\n        }\n      }\n    },\n\n    stop: function() {\n      this.done = true;\n\n      var rootEntry = this.tryEntries[0];\n      var rootRecord = rootEntry.completion;\n      if (rootRecord.type === \"throw\") {\n        throw rootRecord.arg;\n      }\n\n      return this.rval;\n    },\n\n    dispatchException: function(exception) {\n      if (this.done) {\n        throw exception;\n      }\n\n      var context = this;\n      function handle(loc, caught) {\n        record.type = \"throw\";\n        record.arg = exception;\n        context.next = loc;\n\n        if (caught) {\n          // If the dispatched exception was caught by a catch block,\n          // then let that catch block handle the exception normally.\n          context.method = \"next\";\n          context.arg = undefined;\n        }\n\n        return !! caught;\n      }\n\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        var record = entry.completion;\n\n        if (entry.tryLoc === \"root\") {\n          // Exception thrown outside of any try block that could handle\n          // it, so set the completion value of the entire function to\n          // throw the exception.\n          return handle(\"end\");\n        }\n\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\");\n          var hasFinally = hasOwn.call(entry, \"finallyLoc\");\n\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            } else if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            }\n\n          } else if (hasFinally) {\n            if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else {\n            throw new Error(\"try statement without catch or finally\");\n          }\n        }\n      }\n    },\n\n    abrupt: function(type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev &&\n            hasOwn.call(entry, \"finallyLoc\") &&\n            this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n\n      if (finallyEntry &&\n          (type === \"break\" ||\n           type === \"continue\") &&\n          finallyEntry.tryLoc <= arg &&\n          arg <= finallyEntry.finallyLoc) {\n        // Ignore the finally entry if control is not jumping to a\n        // location outside the try/catch block.\n        finallyEntry = null;\n      }\n\n      var record = finallyEntry ? finallyEntry.completion : {};\n      record.type = type;\n      record.arg = arg;\n\n      if (finallyEntry) {\n        this.method = \"next\";\n        this.next = finallyEntry.finallyLoc;\n        return ContinueSentinel;\n      }\n\n      return this.complete(record);\n    },\n\n    complete: function(record, afterLoc) {\n      if (record.type === \"throw\") {\n        throw record.arg;\n      }\n\n      if (record.type === \"break\" ||\n          record.type === \"continue\") {\n        this.next = record.arg;\n      } else if (record.type === \"return\") {\n        this.rval = this.arg = record.arg;\n        this.method = \"return\";\n        this.next = \"end\";\n      } else if (record.type === \"normal\" && afterLoc) {\n        this.next = afterLoc;\n      }\n\n      return ContinueSentinel;\n    },\n\n    finish: function(finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) {\n          this.complete(entry.completion, entry.afterLoc);\n          resetTryEntry(entry);\n          return ContinueSentinel;\n        }\n      }\n    },\n\n    \"catch\": function(tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (record.type === \"throw\") {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n\n      // The context.catch method must only be called with a location\n      // argument that corresponds to a known catch block.\n      throw new Error(\"illegal catch attempt\");\n    },\n\n    delegateYield: function(iterable, resultName, nextLoc) {\n      this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      };\n\n      if (this.method === \"next\") {\n        // Deliberately forget the last sent value so that we don't\n        // accidentally pass it on to the delegate.\n        this.arg = undefined;\n      }\n\n      return ContinueSentinel;\n    }\n  };\n\n  // Regardless of whether this script is executing as a CommonJS module\n  // or not, return the runtime object so that we can declare the variable\n  // regeneratorRuntime in the outer scope, which allows this module to be\n  // injected easily by `bin/regenerator --include-runtime script.js`.\n  return exports;\n\n}(\n  // If this script is executing as a CommonJS module, use module.exports\n  // as the regeneratorRuntime namespace. Otherwise create a new empty\n  // object. Either way, the resulting object will be used to initialize\n  // the regeneratorRuntime variable at the top of this file.\n  typeof module === \"object\" ? module.exports : {}\n));\n\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  // This module should not be running in strict mode, so the above\n  // assignment should always work unless something is misconfigured. Just\n  // in case runtime.js accidentally runs in strict mode, in modern engines\n  // we can explicitly access globalThis. In older engines we can escape\n  // strict mode using a global Function call. This could conceivably fail\n  // if a Content Security Policy forbids using Function, but in that case\n  // the proper solution is to fix the accidental strict mode problem. If\n  // you've misconfigured your bundler to force strict mode and applied a\n  // CSP to forbid Function, and you're not willing to fix either of those\n  // problems, please detail your unique predicament in a GitHub issue.\n  if (typeof globalThis === \"object\") {\n    globalThis.regeneratorRuntime = runtime;\n  } else {\n    Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n  }\n}\n", "module.exports = (prefix, cnt) => (\n  `${prefix}-${cnt}-${Math.random().toString(16).slice(3, 8)}`\n);\n", "const getId = require('./utils/getId');\n\nlet jobCounter = 0;\n\nmodule.exports = ({\n  id: _id,\n  action,\n  payload = {},\n}) => {\n  let id = _id;\n  if (typeof id === 'undefined') {\n    id = getId('Job', jobCounter);\n    jobCounter += 1;\n  }\n\n  return {\n    id,\n    action,\n    payload,\n  };\n};\n", "let logging = false;\n\nexports.logging = logging;\n\nexports.setLogging = (_logging) => {\n  logging = _logging;\n};\n\nexports.log = (...args) => (logging ? console.log.apply(this, args) : null);\n", "const createJob = require('./createJob');\nconst { log } = require('./utils/log');\nconst getId = require('./utils/getId');\n\nlet schedulerCounter = 0;\n\nmodule.exports = () => {\n  const id = getId('Scheduler', schedulerCounter);\n  const workers = {};\n  const runningWorkers = {};\n  let jobQueue = [];\n\n  schedulerCounter += 1;\n\n  const getQueueLen = () => jobQueue.length;\n  const getNumWorkers = () => Object.keys(workers).length;\n\n  const dequeue = () => {\n    if (jobQueue.length !== 0) {\n      const wIds = Object.keys(workers);\n      for (let i = 0; i < wIds.length; i += 1) {\n        if (typeof runningWorkers[wIds[i]] === 'undefined') {\n          jobQueue[0](workers[wIds[i]]);\n          break;\n        }\n      }\n    }\n  };\n\n  const queue = (action, payload) => (\n    new Promise((resolve, reject) => {\n      const job = createJob({ action, payload });\n      jobQueue.push(async (w) => {\n        jobQueue.shift();\n        runningWorkers[w.id] = job;\n        try {\n          resolve(await w[action].apply(this, [...payload, job.id]));\n        } catch (err) {\n          reject(err);\n        } finally {\n          delete runningWorkers[w.id];\n          dequeue();\n        }\n      });\n      log(`[${id}]: Add ${job.id} to JobQueue`);\n      log(`[${id}]: JobQueue length=${jobQueue.length}`);\n      dequeue();\n    })\n  );\n\n  const addWorker = (w) => {\n    workers[w.id] = w;\n    log(`[${id}]: Add ${w.id}`);\n    log(`[${id}]: Number of workers=${getNumWorkers()}`);\n    dequeue();\n    return w.id;\n  };\n\n  const addJob = async (action, ...payload) => {\n    if (getNumWorkers() === 0) {\n      throw Error(`[${id}]: You need to have at least one worker before adding jobs`);\n    }\n    return queue(action, payload);\n  };\n\n  const terminate = async () => {\n    Object.keys(workers).forEach(async (wid) => {\n      await workers[wid].terminate();\n    });\n    jobQueue = [];\n  };\n\n  return {\n    addWorker,\n    addJob,\n    terminate,\n    getQueueLen,\n    getNumWorkers,\n  };\n};\n", "// https://github.com/electron/electron/issues/2288\nfunction isElectron() {\n    // Renderer process\n    if (typeof window !== 'undefined' && typeof window.process === 'object' && window.process.type === 'renderer') {\n        return true;\n    }\n\n    // Main process\n    if (typeof process !== 'undefined' && typeof process.versions === 'object' && !!process.versions.electron) {\n        return true;\n    }\n\n    // Detect the user agent when the `nodeIntegration` option is set to false\n    if (typeof navigator === 'object' && typeof navigator.userAgent === 'string' && navigator.userAgent.indexOf('Electron') >= 0) {\n        return true;\n    }\n\n    return false;\n}\n\nmodule.exports = isElectron;\n", "const isElectron = require('is-electron');\n\nmodule.exports = (key) => {\n  const env = {};\n\n  if (typeof WorkerGlobalScope !== 'undefined') {\n    env.type = 'webworker';\n  } else if (isElectron()) {\n    env.type = 'electron';\n  } else if (typeof document === 'object') {\n    env.type = 'browser';\n  } else if (typeof process === 'object' && typeof require === 'function') {\n    env.type = 'node';\n  }\n\n  if (typeof key === 'undefined') {\n    return env;\n  }\n\n  return env[key];\n};\n", "const isBrowser = require('./getEnvironment')('type') === 'browser';\n\nconst resolveURL = isBrowser ? s => (new URL(s, window.location.href)).href : s => s; // eslint-disable-line\n\nmodule.exports = (options) => {\n  const opts = { ...options };\n  ['corePath', 'workerPath', 'langPath'].forEach((key) => {\n    if (options[key]) {\n      opts[key] = resolveURL(opts[key]);\n    }\n  });\n  return opts;\n};\n", "/**\n * In the recognition result of tesseract, there\n * is a deep JSON object for details, it has around\n *\n * The result of dump.js is a big JSON tree\n * which can be easily serialized (for instance\n * to be sent from a webworker to the main app\n * or through Node's IPC), but we want\n * a (circular) DOM-like interface for walking\n * through the data.\n *\n * @fileoverview DOM-like interface for walking through data\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <j<PERSON><PERSON><EMAIL>>\n */\n\nmodule.exports = (page) => {\n  const blocks = [];\n  const paragraphs = [];\n  const lines = [];\n  const words = [];\n  const symbols = [];\n\n  if (page.blocks) {\n    page.blocks.forEach((block) => {\n      block.paragraphs.forEach((paragraph) => {\n        paragraph.lines.forEach((line) => {\n          line.words.forEach((word) => {\n            word.symbols.forEach((sym) => {\n              symbols.push({\n                ...sym, page, block, paragraph, line, word,\n              });\n            });\n            words.push({\n              ...word, page, block, paragraph, line,\n            });\n          });\n          lines.push({\n            ...line, page, block, paragraph,\n          });\n        });\n        paragraphs.push({\n          ...paragraph, page, block,\n        });\n      });\n      blocks.push({\n        ...block, page,\n      });\n    });\n  }\n\n  return {\n    ...page, blocks, paragraphs, lines, words, symbols,\n  };\n};\n", "/*\n * OEM = OCR Engine Mode, and there are 4 possible modes.\n *\n * By default tesseract.js uses LSTM_ONLY mode.\n *\n */\nmodule.exports = {\n  TESSERACT_ONLY: 0,\n  LSTM_ONLY: 1,\n  TESSERACT_LSTM_COMBINED: 2,\n  DEFAULT: 3,\n};\n", "{\n  \"name\": \"tesseract.js\",\n  \"version\": \"5.1.1\",\n  \"description\": \"Pure Javascript Multilingual OCR\",\n  \"main\": \"src/index.js\",\n  \"types\": \"src/index.d.ts\",\n  \"unpkg\": \"dist/tesseract.min.js\",\n  \"jsdelivr\": \"dist/tesseract.min.js\",\n  \"scripts\": {\n    \"start\": \"node scripts/server.js\",\n    \"build\": \"rimraf dist && webpack --config scripts/webpack.config.prod.js && rollup -c scripts/rollup.esm.mjs\",\n    \"profile:tesseract\": \"webpack-bundle-analyzer dist/tesseract-stats.json\",\n    \"profile:worker\": \"webpack-bundle-analyzer dist/worker-stats.json\",\n    \"prepublishOnly\": \"npm run build\",\n    \"wait\": \"rimraf dist && wait-on http://localhost:3000/dist/tesseract.min.js\",\n    \"test\": \"npm-run-all -p -r start test:all\",\n    \"test:all\": \"npm-run-all wait test:browser:* test:node:all\",\n    \"test:node\": \"nyc mocha --exit --bail --require ./scripts/test-helper.js\",\n    \"test:node:all\": \"npm run test:node -- ./tests/*.test.js\",\n    \"test:browser-tpl\": \"mocha-headless-chrome -a incognito -a no-sandbox -a disable-setuid-sandbox -a disable-logging -t 300000\",\n    \"test:browser:detect\": \"npm run test:browser-tpl -- -f ./tests/detect.test.html\",\n    \"test:browser:recognize\": \"npm run test:browser-tpl -- -f ./tests/recognize.test.html\",\n    \"test:browser:scheduler\": \"npm run test:browser-tpl -- -f ./tests/scheduler.test.html\",\n    \"test:browser:FS\": \"npm run test:browser-tpl -- -f ./tests/FS.test.html\",\n    \"lint\": \"eslint src\",\n    \"lint:fix\": \"eslint --fix src\",\n    \"postinstall\": \"opencollective-postinstall || true\"\n  },\n  \"browser\": {\n    \"./src/worker/node/index.js\": \"./src/worker/browser/index.js\"\n  },\n  \"author\": \"\",\n  \"contributors\": [\n    \"jeromewu\"\n  ],\n  \"license\": \"Apache-2.0\",\n  \"devDependencies\": {\n    \"@babel/core\": \"^7.21.4\",\n    \"@babel/eslint-parser\": \"^7.21.3\",\n    \"@babel/preset-env\": \"^7.21.4\",\n    \"@rollup/plugin-commonjs\": \"^24.1.0\",\n    \"acorn\": \"^8.8.2\",\n    \"babel-loader\": \"^9.1.2\",\n    \"buffer\": \"^6.0.3\",\n    \"cors\": \"^2.8.5\",\n    \"eslint\": \"^7.32.0\",\n    \"eslint-config-airbnb-base\": \"^14.2.1\",\n    \"eslint-plugin-import\": \"^2.27.5\",\n    \"expect.js\": \"^0.3.1\",\n    \"express\": \"^4.18.2\",\n    \"mocha\": \"^10.2.0\",\n    \"mocha-headless-chrome\": \"^4.0.0\",\n    \"npm-run-all\": \"^4.1.5\",\n    \"nyc\": \"^15.1.0\",\n    \"rimraf\": \"^5.0.0\",\n    \"rollup\": \"^3.20.7\",\n    \"wait-on\": \"^7.0.1\",\n    \"webpack\": \"^5.79.0\",\n    \"webpack-bundle-analyzer\": \"^4.8.0\",\n    \"webpack-cli\": \"^5.0.1\",\n    \"webpack-dev-middleware\": \"^6.0.2\",\n    \"rollup-plugin-sourcemaps\": \"^0.6.3\"\n  },\n  \"dependencies\": {\n    \"bmp-js\": \"^0.1.0\",\n    \"idb-keyval\": \"^6.2.0\",\n    \"is-electron\": \"^2.2.2\",\n    \"is-url\": \"^1.2.4\",\n    \"node-fetch\": \"^2.6.9\",\n    \"opencollective-postinstall\": \"^2.0.3\",\n    \"regenerator-runtime\": \"^0.13.3\",\n    \"tesseract.js-core\": \"^5.1.1\",\n    \"wasm-feature-detect\": \"^1.2.11\",\n    \"zlibjs\": \"^0.3.1\"\n  },\n  \"overrides\": {\n    \"@rollup/pluginutils\": \"^5.0.2\"\n  },\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/naptha/tesseract.js.git\"\n  },\n  \"bugs\": {\n    \"url\": \"https://github.com/naptha/tesseract.js/issues\"\n  },\n  \"homepage\": \"https://github.com/naptha/tesseract.js\",\n  \"collective\": {\n    \"type\": \"opencollective\",\n    \"url\": \"https://opencollective.com/tesseractjs\"\n  }\n}\n", "module.exports = {\n  /*\n   * Use BlobURL for worker script by default\n   * TODO: remove this option\n   *\n   */\n  workerBlobURL: true,\n  logger: () => {},\n};\n", "const version = require('../../../package.json').version;\nconst defaultOptions = require('../../constants/defaultOptions');\n\n/*\n * Default options for browser worker\n */\nmodule.exports = {\n  ...defaultOptions,\n  workerPath: `https://cdn.jsdelivr.net/npm/tesseract.js@v${version}/dist/worker.min.js`,\n};\n", "/**\n * spawnWorker\n *\n * @name spawnWorker\n * @function create a new Worker in browser\n * @access public\n */\nmodule.exports = ({ workerPath, workerBlobURL }) => {\n  let worker;\n  if (Blob && URL && workerBlobURL) {\n    const blob = new Blob([`importScripts(\"${workerPath}\");`], {\n      type: 'application/javascript',\n    });\n    worker = new Worker(URL.createObjectURL(blob));\n  } else {\n    worker = new Worker(workerPath);\n  }\n\n  return worker;\n};\n", "/**\n * terminateWorker\n *\n * @name terminateWorker\n * @function terminate worker\n * @access public\n */\nmodule.exports = (worker) => {\n  worker.terminate();\n};\n", "module.exports = (worker, handler) => {\n  worker.onmessage = ({ data }) => { // eslint-disable-line\n    handler(data);\n  };\n};\n", "/**\n * send\n *\n * @name send\n * @function send packet to worker and create a job\n * @access public\n */\nmodule.exports = async (worker, packet) => {\n  worker.postMessage(packet);\n};\n", "/**\n * readFromBlobOrFile\n *\n * @name readFromBlobOrFile\n * @function\n * @access private\n */\nconst readFromBlobOrFile = (blob) => (\n  new Promise((resolve, reject) => {\n    const fileReader = new FileReader();\n    fileReader.onload = () => {\n      resolve(fileReader.result);\n    };\n    fileReader.onerror = ({ target: { error: { code } } }) => {\n      reject(Error(`File could not be read! Code=${code}`));\n    };\n    fileReader.readAsArrayBuffer(blob);\n  })\n);\n\n/**\n * loadImage\n *\n * @name loadImage\n * @function load image from different source\n * @access private\n */\nconst loadImage = async (image) => {\n  let data = image;\n  if (typeof image === 'undefined') {\n    return 'undefined';\n  }\n\n  if (typeof image === 'string') {\n    // Base64 Image\n    if (/data:image\\/([a-zA-Z]*);base64,([^\"]*)/.test(image)) {\n      data = atob(image.split(',')[1])\n        .split('')\n        .map((c) => c.charCodeAt(0));\n    } else {\n      const resp = await fetch(image);\n      data = await resp.arrayBuffer();\n    }\n  } else if (typeof HTMLElement !== 'undefined' && image instanceof HTMLElement) {\n    if (image.tagName === 'IMG') {\n      data = await loadImage(image.src);\n    }\n    if (image.tagName === 'VIDEO') {\n      data = await loadImage(image.poster);\n    }\n    if (image.tagName === 'CANVAS') {\n      await new Promise((resolve) => {\n        image.toBlob(async (blob) => {\n          data = await readFromBlobOrFile(blob);\n          resolve();\n        });\n      });\n    }\n  } else if (typeof OffscreenCanvas !== 'undefined' && image instanceof OffscreenCanvas) {\n    const blob = await image.convertToBlob();\n    data = await readFromBlobOrFile(blob);\n  } else if (image instanceof File || image instanceof Blob) {\n    data = await readFromBlobOrFile(image);\n  }\n\n  return new Uint8Array(data);\n};\n\nmodule.exports = loadImage;\n", "/**\n *\n * Tesseract Worker adapter for browser\n *\n * @fileoverview Tesseract Worker adapter for browser\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <jero<PERSON><PERSON><EMAIL>>\n */\nconst defaultOptions = require('./defaultOptions');\nconst spawnWorker = require('./spawnWorker');\nconst terminateWorker = require('./terminateWorker');\nconst onMessage = require('./onMessage');\nconst send = require('./send');\nconst loadImage = require('./loadImage');\n\nmodule.exports = {\n  defaultOptions,\n  spawnWorker,\n  terminateWorker,\n  onMessage,\n  send,\n  loadImage,\n};\n", "const resolvePaths = require('./utils/resolvePaths');\nconst circularize = require('./utils/circularize');\nconst createJob = require('./createJob');\nconst { log } = require('./utils/log');\nconst getId = require('./utils/getId');\nconst OEM = require('./constants/OEM');\nconst {\n  defaultOptions,\n  spawnWorker,\n  terminateWorker,\n  onMessage,\n  loadImage,\n  send,\n} = require('./worker/node');\n\nlet workerCounter = 0;\n\nmodule.exports = async (langs = 'eng', oem = OEM.LSTM_ONLY, _options = {}, config = {}) => {\n  const id = getId('Worker', workerCounter);\n  const {\n    logger,\n    errorHandler,\n    ...options\n  } = resolvePaths({\n    ...defaultOptions,\n    ..._options,\n  });\n  const resolves = {};\n  const rejects = {};\n\n  // Current langs, oem, and config file.\n  // Used if the user ever re-initializes the worker using `worker.reinitialize`.\n  const currentLangs = typeof langs === 'string' ? langs.split('+') : langs;\n  let currentOem = oem;\n  let currentConfig = config;\n  const lstmOnlyCore = [OEM.DEFAULT, OEM.LSTM_ONLY].includes(oem) && !options.legacyCore;\n\n  let workerResReject;\n  let workerResResolve;\n  const workerRes = new Promise((resolve, reject) => {\n    workerResResolve = resolve;\n    workerResReject = reject;\n  });\n  const workerError = (event) => { workerResReject(event.message); };\n\n  let worker = spawnWorker(options);\n  worker.onerror = workerError;\n\n  workerCounter += 1;\n\n  const setResolve = (promiseId, res) => {\n    resolves[promiseId] = res;\n  };\n\n  const setReject = (promiseId, rej) => {\n    rejects[promiseId] = rej;\n  };\n\n  const startJob = ({ id: jobId, action, payload }) => (\n    new Promise((resolve, reject) => {\n      log(`[${id}]: Start ${jobId}, action=${action}`);\n      // Using both `action` and `jobId` in case user provides non-unique `jobId`.\n      const promiseId = `${action}-${jobId}`;\n      setResolve(promiseId, resolve);\n      setReject(promiseId, reject);\n      send(worker, {\n        workerId: id,\n        jobId,\n        action,\n        payload,\n      });\n    })\n  );\n\n  const load = () => (\n    console.warn('`load` is depreciated and should be removed from code (workers now come pre-loaded)')\n  );\n\n  const loadInternal = (jobId) => (\n    startJob(createJob({\n      id: jobId, action: 'load', payload: { options: { lstmOnly: lstmOnlyCore, corePath: options.corePath, logging: options.logging } },\n    }))\n  );\n\n  const writeText = (path, text, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method: 'writeFile', args: [path, text] },\n    }))\n  );\n\n  const readText = (path, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method: 'readFile', args: [path, { encoding: 'utf8' }] },\n    }))\n  );\n\n  const removeFile = (path, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method: 'unlink', args: [path] },\n    }))\n  );\n\n  const FS = (method, args, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method, args },\n    }))\n  );\n\n  const loadLanguage = () => (\n    console.warn('`loadLanguage` is depreciated and should be removed from code (workers now come with language pre-loaded)')\n  );\n\n  const loadLanguageInternal = (_langs, jobId) => startJob(createJob({\n    id: jobId,\n    action: 'loadLanguage',\n    payload: {\n      langs: _langs,\n      options: {\n        langPath: options.langPath,\n        dataPath: options.dataPath,\n        cachePath: options.cachePath,\n        cacheMethod: options.cacheMethod,\n        gzip: options.gzip,\n        lstmOnly: [OEM.DEFAULT, OEM.LSTM_ONLY].includes(currentOem)\n          && !options.legacyLang,\n      },\n    },\n  }));\n\n  const initialize = () => (\n    console.warn('`initialize` is depreciated and should be removed from code (workers now come pre-initialized)')\n  );\n\n  const initializeInternal = (_langs, _oem, _config, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'initialize',\n      payload: { langs: _langs, oem: _oem, config: _config },\n    }))\n  );\n\n  const reinitialize = (langs = 'eng', oem, config, jobId) => { // eslint-disable-line\n\n    if (lstmOnlyCore && [OEM.TESSERACT_ONLY, OEM.TESSERACT_LSTM_COMBINED].includes(oem)) throw Error('Legacy model requested but code missing.');\n\n    const _oem = oem || currentOem;\n    currentOem = _oem;\n\n    const _config = config || currentConfig;\n    currentConfig = _config;\n\n    // Only load langs that are not already loaded.\n    // This logic fails if the user downloaded the LSTM-only English data for a language\n    // and then uses `worker.reinitialize` to switch to the Legacy engine.\n    // However, the correct data will still be downloaded after initialization fails\n    // and this can be avoided entirely if the user loads the correct data ahead of time.\n    const langsArr = typeof langs === 'string' ? langs.split('+') : langs;\n    const _langs = langsArr.filter((x) => !currentLangs.includes(x));\n    currentLangs.push(..._langs);\n\n    if (_langs.length > 0) {\n      return loadLanguageInternal(_langs, jobId)\n        .then(() => initializeInternal(langs, _oem, _config, jobId));\n    }\n\n    return initializeInternal(langs, _oem, _config, jobId);\n  };\n\n  const setParameters = (params = {}, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'setParameters',\n      payload: { params },\n    }))\n  );\n\n  const recognize = async (image, opts = {}, output = {\n    blocks: true, text: true, hocr: true, tsv: true,\n  }, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'recognize',\n      payload: { image: await loadImage(image), options: opts, output },\n    }))\n  );\n\n  const getPDF = (title = 'Tesseract OCR Result', textonly = false, jobId) => {\n    console.log('`getPDF` function is depreciated. `recognize` option `savePDF` should be used instead.');\n    return startJob(createJob({\n      id: jobId,\n      action: 'getPDF',\n      payload: { title, textonly },\n    }));\n  };\n\n  const detect = async (image, jobId) => {\n    if (lstmOnlyCore) throw Error('`worker.detect` requires Legacy model, which was not loaded.');\n\n    return startJob(createJob({\n      id: jobId,\n      action: 'detect',\n      payload: { image: await loadImage(image) },\n    }));\n  };\n\n  const terminate = async () => {\n    if (worker !== null) {\n      /*\n      await startJob(createJob({\n        id: jobId,\n        action: 'terminate',\n      }));\n      */\n      terminateWorker(worker);\n      worker = null;\n    }\n    return Promise.resolve();\n  };\n\n  onMessage(worker, ({\n    workerId, jobId, status, action, data,\n  }) => {\n    const promiseId = `${action}-${jobId}`;\n    if (status === 'resolve') {\n      log(`[${workerId}]: Complete ${jobId}`);\n      let d = data;\n      if (action === 'recognize') {\n        d = circularize(data);\n      } else if (action === 'getPDF') {\n        d = Array.from({ ...data, length: Object.keys(data).length });\n      }\n      resolves[promiseId]({ jobId, data: d });\n    } else if (status === 'reject') {\n      rejects[promiseId](data);\n      if (action === 'load') workerResReject(data);\n      if (errorHandler) {\n        errorHandler(data);\n      } else {\n        throw Error(data);\n      }\n    } else if (status === 'progress') {\n      logger({ ...data, userJobId: jobId });\n    }\n  });\n\n  const resolveObj = {\n    id,\n    worker,\n    setResolve,\n    setReject,\n    load,\n    writeText,\n    readText,\n    removeFile,\n    FS,\n    loadLanguage,\n    initialize,\n    reinitialize,\n    setParameters,\n    recognize,\n    getPDF,\n    detect,\n    terminate,\n  };\n\n  loadInternal()\n    .then(() => loadLanguageInternal(langs))\n    .then(() => initializeInternal(langs, oem, config))\n    .then(() => workerResResolve(resolveObj))\n    .catch(() => {});\n\n  return workerRes;\n};\n", "const createWorker = require('./createWorker');\n\nconst recognize = async (image, langs, options) => {\n  const worker = await createWorker(langs, 1, options);\n  return worker.recognize(image)\n    .finally(async () => {\n      await worker.terminate();\n    });\n};\n\nconst detect = async (image, options) => {\n  const worker = await createWorker('osd', 0, options);\n  return worker.detect(image)\n    .finally(async () => {\n      await worker.terminate();\n    });\n};\n\nmodule.exports = {\n  recognize,\n  detect,\n};\n", "/*\n * languages with existing tesseract traineddata\n * https://tesseract-ocr.github.io/tessdoc/Data-Files#data-files-for-version-400-november-29-2016\n */\n\n/**\n * @typedef {object} Languages\n * @property {string} AFR Afrikaans\n * @property {string} AMH Amharic\n * @property {string} ARA Arabic\n * @property {string} ASM Assamese\n * @property {string} AZE Azerbaijani\n * @property {string} AZE_CYRL Azerbaijani - Cyrillic\n * @property {string} BEL Belarusian\n * @property {string} BEN Bengali\n * @property {string} BOD Tibetan\n * @property {string} BOS Bosnian\n * @property {string} BUL Bulgarian\n * @property {string} CAT Catalan; Valencian\n * @property {string} CEB Cebuano\n * @property {string} CES Czech\n * @property {string} CHI_SIM Chinese - Simplified\n * @property {string} CHI_TRA Chinese - Traditional\n * @property {string} CHR Cherokee\n * @property {string} CYM Welsh\n * @property {string} DAN Danish\n * @property {string} DEU German\n * @property {string} DZO Dzongkha\n * @property {string} ELL Greek, Modern (1453-)\n * @property {string} ENG English\n * @property {string} ENM English, Middle (1100-1500)\n * @property {string} EPO Esperanto\n * @property {string} EST Estonian\n * @property {string} EUS Basque\n * @property {string} FAS Persian\n * @property {string} FIN Finnish\n * @property {string} FRA French\n * @property {string} FRK German Fraktur\n * @property {string} FRM French, Middle (ca. 1400-1600)\n * @property {string} GLE Irish\n * @property {string} GLG Galician\n * @property {string} GRC Greek, Ancient (-1453)\n * @property {string} GUJ Gujarati\n * @property {string} HAT Haitian; Haitian Creole\n * @property {string} HEB Hebrew\n * @property {string} HIN Hindi\n * @property {string} HRV Croatian\n * @property {string} HUN Hungarian\n * @property {string} IKU Inuktitut\n * @property {string} IND Indonesian\n * @property {string} ISL Icelandic\n * @property {string} ITA Italian\n * @property {string} ITA_OLD Italian - Old\n * @property {string} JAV Javanese\n * @property {string} JPN Japanese\n * @property {string} KAN Kannada\n * @property {string} KAT Georgian\n * @property {string} KAT_OLD Georgian - Old\n * @property {string} KAZ Kazakh\n * @property {string} KHM Central Khmer\n * @property {string} KIR Kirghiz; Kyrgyz\n * @property {string} KOR Korean\n * @property {string} KUR Kurdish\n * @property {string} LAO Lao\n * @property {string} LAT Latin\n * @property {string} LAV Latvian\n * @property {string} LIT Lithuanian\n * @property {string} MAL Malayalam\n * @property {string} MAR Marathi\n * @property {string} MKD Macedonian\n * @property {string} MLT Maltese\n * @property {string} MSA Malay\n * @property {string} MYA Burmese\n * @property {string} NEP Nepali\n * @property {string} NLD Dutch; Flemish\n * @property {string} NOR Norwegian\n * @property {string} ORI Oriya\n * @property {string} PAN Panjabi; Punjabi\n * @property {string} POL Polish\n * @property {string} POR Portuguese\n * @property {string} PUS Pushto; Pashto\n * @property {string} RON Romanian; Moldavian; Moldovan\n * @property {string} RUS Russian\n * @property {string} SAN Sanskrit\n * @property {string} SIN Sinhala; Sinhalese\n * @property {string} SLK Slovak\n * @property {string} SLV Slovenian\n * @property {string} SPA Spanish; Castilian\n * @property {string} SPA_OLD Spanish; Castilian - Old\n * @property {string} SQI Albanian\n * @property {string} SRP Serbian\n * @property {string} SRP_LATN Serbian - Latin\n * @property {string} SWA Swahili\n * @property {string} SWE Swedish\n * @property {string} SYR Syriac\n * @property {string} TAM Tamil\n * @property {string} TEL Telugu\n * @property {string} TGK Tajik\n * @property {string} TGL Tagalog\n * @property {string} THA Thai\n * @property {string} TIR Tigrinya\n * @property {string} TUR Turkish\n * @property {string} UIG Uighur; Uyghur\n * @property {string} UKR Ukrainian\n * @property {string} URD Urdu\n * @property {string} UZB Uzbek\n * @property {string} UZB_CYRL Uzbek - Cyrillic\n * @property {string} VIE Vietnamese\n * @property {string} YID Yiddish\n */\n\n/**\n  * @type {Languages}\n  */\nmodule.exports = {\n  AFR: 'afr',\n  AMH: 'amh',\n  ARA: 'ara',\n  ASM: 'asm',\n  AZE: 'aze',\n  AZE_CYRL: 'aze_cyrl',\n  BEL: 'bel',\n  BEN: 'ben',\n  BOD: 'bod',\n  BOS: 'bos',\n  BUL: 'bul',\n  CAT: 'cat',\n  CEB: 'ceb',\n  CES: 'ces',\n  CHI_SIM: 'chi_sim',\n  CHI_TRA: 'chi_tra',\n  CHR: 'chr',\n  CYM: 'cym',\n  DAN: 'dan',\n  DEU: 'deu',\n  DZO: 'dzo',\n  ELL: 'ell',\n  ENG: 'eng',\n  ENM: 'enm',\n  EPO: 'epo',\n  EST: 'est',\n  EUS: 'eus',\n  FAS: 'fas',\n  FIN: 'fin',\n  FRA: 'fra',\n  FRK: 'frk',\n  FRM: 'frm',\n  GLE: 'gle',\n  GLG: 'glg',\n  GRC: 'grc',\n  GUJ: 'guj',\n  HAT: 'hat',\n  HEB: 'heb',\n  HIN: 'hin',\n  HRV: 'hrv',\n  HUN: 'hun',\n  IKU: 'iku',\n  IND: 'ind',\n  ISL: 'isl',\n  ITA: 'ita',\n  ITA_OLD: 'ita_old',\n  JAV: 'jav',\n  JPN: 'jpn',\n  KAN: 'kan',\n  KAT: 'kat',\n  KAT_OLD: 'kat_old',\n  KAZ: 'kaz',\n  KHM: 'khm',\n  KIR: 'kir',\n  KOR: 'kor',\n  KUR: 'kur',\n  LAO: 'lao',\n  LAT: 'lat',\n  LAV: 'lav',\n  LIT: 'lit',\n  MAL: 'mal',\n  MAR: 'mar',\n  MKD: 'mkd',\n  MLT: 'mlt',\n  MSA: 'msa',\n  MYA: 'mya',\n  NEP: 'nep',\n  NLD: 'nld',\n  NOR: 'nor',\n  ORI: 'ori',\n  PAN: 'pan',\n  POL: 'pol',\n  POR: 'por',\n  PUS: 'pus',\n  RON: 'ron',\n  RUS: 'rus',\n  SAN: 'san',\n  SIN: 'sin',\n  SLK: 'slk',\n  SLV: 'slv',\n  SPA: 'spa',\n  SPA_OLD: 'spa_old',\n  SQI: 'sqi',\n  SRP: 'srp',\n  SRP_LATN: 'srp_latn',\n  SWA: 'swa',\n  SWE: 'swe',\n  SYR: 'syr',\n  TAM: 'tam',\n  TEL: 'tel',\n  TGK: 'tgk',\n  TGL: 'tgl',\n  THA: 'tha',\n  TIR: 'tir',\n  TUR: 'tur',\n  UIG: 'uig',\n  UKR: 'ukr',\n  URD: 'urd',\n  UZB: 'uzb',\n  UZB_CYRL: 'uzb_cyrl',\n  VIE: 'vie',\n  YID: 'yid',\n};\n", "/*\n * PSM = Page Segmentation Mode\n */\nmodule.exports = {\n  OSD_ONLY: '0',\n  AUTO_OSD: '1',\n  AUTO_ONLY: '2',\n  AUTO: '3',\n  SINGLE_COLUMN: '4',\n  SINGLE_BLOCK_VERT_TEXT: '5',\n  SINGLE_BLOCK: '6',\n  SINGLE_LINE: '7',\n  SINGLE_WORD: '8',\n  CIRCLE_WORD: '9',\n  SINGLE_CHAR: '10',\n  SPARSE_TEXT: '11',\n  SPARSE_TEXT_OSD: '12',\n  RAW_LINE: '13',\n};\n", "/**\n *\n * Entry point for tesseract.js, should be the entry when bundling.\n *\n * @fileoverview entry point for tesseract.js\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <jero<PERSON><PERSON><EMAIL>>\n */\nrequire('regenerator-runtime/runtime');\nconst createScheduler = require('./createScheduler');\nconst createWorker = require('./createWorker');\nconst Tesseract = require('./Tesseract');\nconst languages = require('./constants/languages');\nconst OEM = require('./constants/OEM');\nconst PSM = require('./constants/PSM');\nconst { setLogging } = require('./utils/log');\n\nmodule.exports = {\n  languages,\n  OEM,\n  PSM,\n  createScheduler,\n  createWorker,\n  setLogging,\n  ...Tesseract,\n};\n"], "mappings": ";;;;;;AAAA;AAAA;AAOA,QAAI,UAAW,SAAUA,UAAS;AAChC;AAEA,UAAI,KAAK,OAAO;AAChB,UAAI,SAAS,GAAG;AAChB,UAAI,iBAAiB,OAAO,kBAAkB,SAAU,KAAK,KAAK,MAAM;AAAE,YAAI,GAAG,IAAI,KAAK;AAAA,MAAO;AACjG,UAAI;AACJ,UAAI,UAAU,OAAO,WAAW,aAAa,SAAS,CAAC;AACvD,UAAI,iBAAiB,QAAQ,YAAY;AACzC,UAAI,sBAAsB,QAAQ,iBAAiB;AACnD,UAAI,oBAAoB,QAAQ,eAAe;AAE/C,eAAS,OAAO,KAAK,KAAK,OAAO;AAC/B,eAAO,eAAe,KAAK,KAAK;AAAA,UAC9B;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,UAAU;AAAA,QACZ,CAAC;AACD,eAAO,IAAI,GAAG;AAAA,MAChB;AACA,UAAI;AAEF,eAAO,CAAC,GAAG,EAAE;AAAA,MACf,SAAS,KAAK;AACZ,iBAAS,SAAS,KAAK,KAAK,OAAO;AACjC,iBAAO,IAAI,GAAG,IAAI;AAAA,QACpB;AAAA,MACF;AAEA,eAAS,KAAK,SAAS,SAAS,MAAM,aAAa;AAEjD,YAAI,iBAAiB,WAAW,QAAQ,qBAAqB,YAAY,UAAU;AACnF,YAAI,YAAY,OAAO,OAAO,eAAe,SAAS;AACtD,YAAI,UAAU,IAAI,QAAQ,eAAe,CAAC,CAAC;AAI3C,uBAAe,WAAW,WAAW,EAAE,OAAO,iBAAiB,SAAS,MAAM,OAAO,EAAE,CAAC;AAExF,eAAO;AAAA,MACT;AACA,MAAAA,SAAQ,OAAO;AAYf,eAAS,SAAS,IAAI,KAAK,KAAK;AAC9B,YAAI;AACF,iBAAO,EAAE,MAAM,UAAU,KAAK,GAAG,KAAK,KAAK,GAAG,EAAE;AAAA,QAClD,SAAS,KAAK;AACZ,iBAAO,EAAE,MAAM,SAAS,KAAK,IAAI;AAAA,QACnC;AAAA,MACF;AAEA,UAAI,yBAAyB;AAC7B,UAAI,yBAAyB;AAC7B,UAAI,oBAAoB;AACxB,UAAI,oBAAoB;AAIxB,UAAI,mBAAmB,CAAC;AAMxB,eAAS,YAAY;AAAA,MAAC;AACtB,eAAS,oBAAoB;AAAA,MAAC;AAC9B,eAAS,6BAA6B;AAAA,MAAC;AAIvC,UAAI,oBAAoB,CAAC;AACzB,aAAO,mBAAmB,gBAAgB,WAAY;AACpD,eAAO;AAAA,MACT,CAAC;AAED,UAAI,WAAW,OAAO;AACtB,UAAI,0BAA0B,YAAY,SAAS,SAAS,OAAO,CAAC,CAAC,CAAC,CAAC;AACvE,UAAI,2BACA,4BAA4B,MAC5B,OAAO,KAAK,yBAAyB,cAAc,GAAG;AAGxD,4BAAoB;AAAA,MACtB;AAEA,UAAI,KAAK,2BAA2B,YAClC,UAAU,YAAY,OAAO,OAAO,iBAAiB;AACvD,wBAAkB,YAAY;AAC9B,qBAAe,IAAI,eAAe,EAAE,OAAO,4BAA4B,cAAc,KAAK,CAAC;AAC3F;AAAA,QACE;AAAA,QACA;AAAA,QACA,EAAE,OAAO,mBAAmB,cAAc,KAAK;AAAA,MACjD;AACA,wBAAkB,cAAc;AAAA,QAC9B;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAIA,eAAS,sBAAsB,WAAW;AACxC,SAAC,QAAQ,SAAS,QAAQ,EAAE,QAAQ,SAAS,QAAQ;AACnD,iBAAO,WAAW,QAAQ,SAAS,KAAK;AACtC,mBAAO,KAAK,QAAQ,QAAQ,GAAG;AAAA,UACjC,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,MAAAA,SAAQ,sBAAsB,SAAS,QAAQ;AAC7C,YAAI,OAAO,OAAO,WAAW,cAAc,OAAO;AAClD,eAAO,OACH,SAAS;AAAA;AAAA,SAGR,KAAK,eAAe,KAAK,UAAU,sBACpC;AAAA,MACN;AAEA,MAAAA,SAAQ,OAAO,SAAS,QAAQ;AAC9B,YAAI,OAAO,gBAAgB;AACzB,iBAAO,eAAe,QAAQ,0BAA0B;AAAA,QAC1D,OAAO;AACL,iBAAO,YAAY;AACnB,iBAAO,QAAQ,mBAAmB,mBAAmB;AAAA,QACvD;AACA,eAAO,YAAY,OAAO,OAAO,EAAE;AACnC,eAAO;AAAA,MACT;AAMA,MAAAA,SAAQ,QAAQ,SAAS,KAAK;AAC5B,eAAO,EAAE,SAAS,IAAI;AAAA,MACxB;AAEA,eAAS,cAAc,WAAW,aAAa;AAC7C,iBAAS,OAAO,QAAQ,KAAK,SAAS,QAAQ;AAC5C,cAAI,SAAS,SAAS,UAAU,MAAM,GAAG,WAAW,GAAG;AACvD,cAAI,OAAO,SAAS,SAAS;AAC3B,mBAAO,OAAO,GAAG;AAAA,UACnB,OAAO;AACL,gBAAI,SAAS,OAAO;AACpB,gBAAI,QAAQ,OAAO;AACnB,gBAAI,SACA,OAAO,UAAU,YACjB,OAAO,KAAK,OAAO,SAAS,GAAG;AACjC,qBAAO,YAAY,QAAQ,MAAM,OAAO,EAAE,KAAK,SAASC,QAAO;AAC7D,uBAAO,QAAQA,QAAO,SAAS,MAAM;AAAA,cACvC,GAAG,SAAS,KAAK;AACf,uBAAO,SAAS,KAAK,SAAS,MAAM;AAAA,cACtC,CAAC;AAAA,YACH;AAEA,mBAAO,YAAY,QAAQ,KAAK,EAAE,KAAK,SAAS,WAAW;AAIzD,qBAAO,QAAQ;AACf,sBAAQ,MAAM;AAAA,YAChB,GAAG,SAAS,OAAO;AAGjB,qBAAO,OAAO,SAAS,OAAO,SAAS,MAAM;AAAA,YAC/C,CAAC;AAAA,UACH;AAAA,QACF;AAEA,YAAI;AAEJ,iBAAS,QAAQ,QAAQ,KAAK;AAC5B,mBAAS,6BAA6B;AACpC,mBAAO,IAAI,YAAY,SAAS,SAAS,QAAQ;AAC/C,qBAAO,QAAQ,KAAK,SAAS,MAAM;AAAA,YACrC,CAAC;AAAA,UACH;AAEA,iBAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAaL,kBAAkB,gBAAgB;AAAA,YAChC;AAAA;AAAA;AAAA,YAGA;AAAA,UACF,IAAI,2BAA2B;AAAA,QACnC;AAIA,uBAAe,MAAM,WAAW,EAAE,OAAO,QAAQ,CAAC;AAAA,MACpD;AAEA,4BAAsB,cAAc,SAAS;AAC7C,aAAO,cAAc,WAAW,qBAAqB,WAAY;AAC/D,eAAO;AAAA,MACT,CAAC;AACD,MAAAD,SAAQ,gBAAgB;AAKxB,MAAAA,SAAQ,QAAQ,SAAS,SAAS,SAAS,MAAM,aAAa,aAAa;AACzE,YAAI,gBAAgB,OAAQ,eAAc;AAE1C,YAAI,OAAO,IAAI;AAAA,UACb,KAAK,SAAS,SAAS,MAAM,WAAW;AAAA,UACxC;AAAA,QACF;AAEA,eAAOA,SAAQ,oBAAoB,OAAO,IACtC,OACA,KAAK,KAAK,EAAE,KAAK,SAAS,QAAQ;AAChC,iBAAO,OAAO,OAAO,OAAO,QAAQ,KAAK,KAAK;AAAA,QAChD,CAAC;AAAA,MACP;AAEA,eAAS,iBAAiB,SAAS,MAAM,SAAS;AAChD,YAAI,QAAQ;AAEZ,eAAO,SAAS,OAAO,QAAQ,KAAK;AAClC,cAAI,UAAU,mBAAmB;AAC/B,kBAAM,IAAI,MAAM,8BAA8B;AAAA,UAChD;AAEA,cAAI,UAAU,mBAAmB;AAC/B,gBAAI,WAAW,SAAS;AACtB,oBAAM;AAAA,YACR;AAIA,mBAAO,WAAW;AAAA,UACpB;AAEA,kBAAQ,SAAS;AACjB,kBAAQ,MAAM;AAEd,iBAAO,MAAM;AACX,gBAAI,WAAW,QAAQ;AACvB,gBAAI,UAAU;AACZ,kBAAI,iBAAiB,oBAAoB,UAAU,OAAO;AAC1D,kBAAI,gBAAgB;AAClB,oBAAI,mBAAmB,iBAAkB;AACzC,uBAAO;AAAA,cACT;AAAA,YACF;AAEA,gBAAI,QAAQ,WAAW,QAAQ;AAG7B,sBAAQ,OAAO,QAAQ,QAAQ,QAAQ;AAAA,YAEzC,WAAW,QAAQ,WAAW,SAAS;AACrC,kBAAI,UAAU,wBAAwB;AACpC,wBAAQ;AACR,sBAAM,QAAQ;AAAA,cAChB;AAEA,sBAAQ,kBAAkB,QAAQ,GAAG;AAAA,YAEvC,WAAW,QAAQ,WAAW,UAAU;AACtC,sBAAQ,OAAO,UAAU,QAAQ,GAAG;AAAA,YACtC;AAEA,oBAAQ;AAER,gBAAI,SAAS,SAAS,SAAS,MAAM,OAAO;AAC5C,gBAAI,OAAO,SAAS,UAAU;AAG5B,sBAAQ,QAAQ,OACZ,oBACA;AAEJ,kBAAI,OAAO,QAAQ,kBAAkB;AACnC;AAAA,cACF;AAEA,qBAAO;AAAA,gBACL,OAAO,OAAO;AAAA,gBACd,MAAM,QAAQ;AAAA,cAChB;AAAA,YAEF,WAAW,OAAO,SAAS,SAAS;AAClC,sBAAQ;AAGR,sBAAQ,SAAS;AACjB,sBAAQ,MAAM,OAAO;AAAA,YACvB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAMA,eAAS,oBAAoB,UAAU,SAAS;AAC9C,YAAI,aAAa,QAAQ;AACzB,YAAI,SAAS,SAAS,SAAS,UAAU;AACzC,YAAI,WAAW,WAAW;AAIxB,kBAAQ,WAAW;AAGnB,cAAI,eAAe,WAAW,SAAS,SAAS,QAAQ,GAAG;AAGzD,oBAAQ,SAAS;AACjB,oBAAQ,MAAM;AACd,gCAAoB,UAAU,OAAO;AAErC,gBAAI,QAAQ,WAAW,SAAS;AAG9B,qBAAO;AAAA,YACT;AAAA,UACF;AACA,cAAI,eAAe,UAAU;AAC3B,oBAAQ,SAAS;AACjB,oBAAQ,MAAM,IAAI;AAAA,cAChB,sCAAsC,aAAa;AAAA,YAAU;AAAA,UACjE;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,SAAS,QAAQ,SAAS,UAAU,QAAQ,GAAG;AAE5D,YAAI,OAAO,SAAS,SAAS;AAC3B,kBAAQ,SAAS;AACjB,kBAAQ,MAAM,OAAO;AACrB,kBAAQ,WAAW;AACnB,iBAAO;AAAA,QACT;AAEA,YAAI,OAAO,OAAO;AAElB,YAAI,CAAE,MAAM;AACV,kBAAQ,SAAS;AACjB,kBAAQ,MAAM,IAAI,UAAU,kCAAkC;AAC9D,kBAAQ,WAAW;AACnB,iBAAO;AAAA,QACT;AAEA,YAAI,KAAK,MAAM;AAGb,kBAAQ,SAAS,UAAU,IAAI,KAAK;AAGpC,kBAAQ,OAAO,SAAS;AAQxB,cAAI,QAAQ,WAAW,UAAU;AAC/B,oBAAQ,SAAS;AACjB,oBAAQ,MAAM;AAAA,UAChB;AAAA,QAEF,OAAO;AAEL,iBAAO;AAAA,QACT;AAIA,gBAAQ,WAAW;AACnB,eAAO;AAAA,MACT;AAIA,4BAAsB,EAAE;AAExB,aAAO,IAAI,mBAAmB,WAAW;AAOzC,aAAO,IAAI,gBAAgB,WAAW;AACpC,eAAO;AAAA,MACT,CAAC;AAED,aAAO,IAAI,YAAY,WAAW;AAChC,eAAO;AAAA,MACT,CAAC;AAED,eAAS,aAAa,MAAM;AAC1B,YAAI,QAAQ,EAAE,QAAQ,KAAK,CAAC,EAAE;AAE9B,YAAI,KAAK,MAAM;AACb,gBAAM,WAAW,KAAK,CAAC;AAAA,QACzB;AAEA,YAAI,KAAK,MAAM;AACb,gBAAM,aAAa,KAAK,CAAC;AACzB,gBAAM,WAAW,KAAK,CAAC;AAAA,QACzB;AAEA,aAAK,WAAW,KAAK,KAAK;AAAA,MAC5B;AAEA,eAAS,cAAc,OAAO;AAC5B,YAAI,SAAS,MAAM,cAAc,CAAC;AAClC,eAAO,OAAO;AACd,eAAO,OAAO;AACd,cAAM,aAAa;AAAA,MACrB;AAEA,eAAS,QAAQ,aAAa;AAI5B,aAAK,aAAa,CAAC,EAAE,QAAQ,OAAO,CAAC;AACrC,oBAAY,QAAQ,cAAc,IAAI;AACtC,aAAK,MAAM,IAAI;AAAA,MACjB;AAEA,MAAAA,SAAQ,OAAO,SAAS,KAAK;AAC3B,YAAI,SAAS,OAAO,GAAG;AACvB,YAAI,OAAO,CAAC;AACZ,iBAAS,OAAO,QAAQ;AACtB,eAAK,KAAK,GAAG;AAAA,QACf;AACA,aAAK,QAAQ;AAIb,eAAO,SAAS,OAAO;AACrB,iBAAO,KAAK,QAAQ;AAClB,gBAAIE,OAAM,KAAK,IAAI;AACnB,gBAAIA,QAAO,QAAQ;AACjB,mBAAK,QAAQA;AACb,mBAAK,OAAO;AACZ,qBAAO;AAAA,YACT;AAAA,UACF;AAKA,eAAK,OAAO;AACZ,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,eAAS,OAAO,UAAU;AACxB,YAAI,UAAU;AACZ,cAAI,iBAAiB,SAAS,cAAc;AAC5C,cAAI,gBAAgB;AAClB,mBAAO,eAAe,KAAK,QAAQ;AAAA,UACrC;AAEA,cAAI,OAAO,SAAS,SAAS,YAAY;AACvC,mBAAO;AAAA,UACT;AAEA,cAAI,CAAC,MAAM,SAAS,MAAM,GAAG;AAC3B,gBAAI,IAAI,IAAI,OAAO,SAASC,QAAO;AACjC,qBAAO,EAAE,IAAI,SAAS,QAAQ;AAC5B,oBAAI,OAAO,KAAK,UAAU,CAAC,GAAG;AAC5B,kBAAAA,MAAK,QAAQ,SAAS,CAAC;AACvB,kBAAAA,MAAK,OAAO;AACZ,yBAAOA;AAAA,gBACT;AAAA,cACF;AAEA,cAAAA,MAAK,QAAQ;AACb,cAAAA,MAAK,OAAO;AAEZ,qBAAOA;AAAA,YACT;AAEA,mBAAO,KAAK,OAAO;AAAA,UACrB;AAAA,QACF;AAGA,eAAO,EAAE,MAAM,WAAW;AAAA,MAC5B;AACA,MAAAH,SAAQ,SAAS;AAEjB,eAAS,aAAa;AACpB,eAAO,EAAE,OAAO,WAAW,MAAM,KAAK;AAAA,MACxC;AAEA,cAAQ,YAAY;AAAA,QAClB,aAAa;AAAA,QAEb,OAAO,SAAS,eAAe;AAC7B,eAAK,OAAO;AACZ,eAAK,OAAO;AAGZ,eAAK,OAAO,KAAK,QAAQ;AACzB,eAAK,OAAO;AACZ,eAAK,WAAW;AAEhB,eAAK,SAAS;AACd,eAAK,MAAM;AAEX,eAAK,WAAW,QAAQ,aAAa;AAErC,cAAI,CAAC,eAAe;AAClB,qBAAS,QAAQ,MAAM;AAErB,kBAAI,KAAK,OAAO,CAAC,MAAM,OACnB,OAAO,KAAK,MAAM,IAAI,KACtB,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG;AAC1B,qBAAK,IAAI,IAAI;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QAEA,MAAM,WAAW;AACf,eAAK,OAAO;AAEZ,cAAI,YAAY,KAAK,WAAW,CAAC;AACjC,cAAI,aAAa,UAAU;AAC3B,cAAI,WAAW,SAAS,SAAS;AAC/B,kBAAM,WAAW;AAAA,UACnB;AAEA,iBAAO,KAAK;AAAA,QACd;AAAA,QAEA,mBAAmB,SAAS,WAAW;AACrC,cAAI,KAAK,MAAM;AACb,kBAAM;AAAA,UACR;AAEA,cAAI,UAAU;AACd,mBAAS,OAAO,KAAK,QAAQ;AAC3B,mBAAO,OAAO;AACd,mBAAO,MAAM;AACb,oBAAQ,OAAO;AAEf,gBAAI,QAAQ;AAGV,sBAAQ,SAAS;AACjB,sBAAQ,MAAM;AAAA,YAChB;AAEA,mBAAO,CAAC,CAAE;AAAA,UACZ;AAEA,mBAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,gBAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,gBAAI,SAAS,MAAM;AAEnB,gBAAI,MAAM,WAAW,QAAQ;AAI3B,qBAAO,OAAO,KAAK;AAAA,YACrB;AAEA,gBAAI,MAAM,UAAU,KAAK,MAAM;AAC7B,kBAAI,WAAW,OAAO,KAAK,OAAO,UAAU;AAC5C,kBAAI,aAAa,OAAO,KAAK,OAAO,YAAY;AAEhD,kBAAI,YAAY,YAAY;AAC1B,oBAAI,KAAK,OAAO,MAAM,UAAU;AAC9B,yBAAO,OAAO,MAAM,UAAU,IAAI;AAAA,gBACpC,WAAW,KAAK,OAAO,MAAM,YAAY;AACvC,yBAAO,OAAO,MAAM,UAAU;AAAA,gBAChC;AAAA,cAEF,WAAW,UAAU;AACnB,oBAAI,KAAK,OAAO,MAAM,UAAU;AAC9B,yBAAO,OAAO,MAAM,UAAU,IAAI;AAAA,gBACpC;AAAA,cAEF,WAAW,YAAY;AACrB,oBAAI,KAAK,OAAO,MAAM,YAAY;AAChC,yBAAO,OAAO,MAAM,UAAU;AAAA,gBAChC;AAAA,cAEF,OAAO;AACL,sBAAM,IAAI,MAAM,wCAAwC;AAAA,cAC1D;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QAEA,QAAQ,SAAS,MAAM,KAAK;AAC1B,mBAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,gBAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,gBAAI,MAAM,UAAU,KAAK,QACrB,OAAO,KAAK,OAAO,YAAY,KAC/B,KAAK,OAAO,MAAM,YAAY;AAChC,kBAAI,eAAe;AACnB;AAAA,YACF;AAAA,UACF;AAEA,cAAI,iBACC,SAAS,WACT,SAAS,eACV,aAAa,UAAU,OACvB,OAAO,aAAa,YAAY;AAGlC,2BAAe;AAAA,UACjB;AAEA,cAAI,SAAS,eAAe,aAAa,aAAa,CAAC;AACvD,iBAAO,OAAO;AACd,iBAAO,MAAM;AAEb,cAAI,cAAc;AAChB,iBAAK,SAAS;AACd,iBAAK,OAAO,aAAa;AACzB,mBAAO;AAAA,UACT;AAEA,iBAAO,KAAK,SAAS,MAAM;AAAA,QAC7B;AAAA,QAEA,UAAU,SAAS,QAAQ,UAAU;AACnC,cAAI,OAAO,SAAS,SAAS;AAC3B,kBAAM,OAAO;AAAA,UACf;AAEA,cAAI,OAAO,SAAS,WAChB,OAAO,SAAS,YAAY;AAC9B,iBAAK,OAAO,OAAO;AAAA,UACrB,WAAW,OAAO,SAAS,UAAU;AACnC,iBAAK,OAAO,KAAK,MAAM,OAAO;AAC9B,iBAAK,SAAS;AACd,iBAAK,OAAO;AAAA,UACd,WAAW,OAAO,SAAS,YAAY,UAAU;AAC/C,iBAAK,OAAO;AAAA,UACd;AAEA,iBAAO;AAAA,QACT;AAAA,QAEA,QAAQ,SAAS,YAAY;AAC3B,mBAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,gBAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,gBAAI,MAAM,eAAe,YAAY;AACnC,mBAAK,SAAS,MAAM,YAAY,MAAM,QAAQ;AAC9C,4BAAc,KAAK;AACnB,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,QAEA,SAAS,SAAS,QAAQ;AACxB,mBAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,gBAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,gBAAI,MAAM,WAAW,QAAQ;AAC3B,kBAAI,SAAS,MAAM;AACnB,kBAAI,OAAO,SAAS,SAAS;AAC3B,oBAAI,SAAS,OAAO;AACpB,8BAAc,KAAK;AAAA,cACrB;AACA,qBAAO;AAAA,YACT;AAAA,UACF;AAIA,gBAAM,IAAI,MAAM,uBAAuB;AAAA,QACzC;AAAA,QAEA,eAAe,SAAS,UAAU,YAAY,SAAS;AACrD,eAAK,WAAW;AAAA,YACd,UAAU,OAAO,QAAQ;AAAA,YACzB;AAAA,YACA;AAAA,UACF;AAEA,cAAI,KAAK,WAAW,QAAQ;AAG1B,iBAAK,MAAM;AAAA,UACb;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAMA,aAAOA;AAAA,IAET;AAAA;AAAA;AAAA;AAAA;AAAA,MAKE,OAAO,WAAW,WAAW,OAAO,UAAU,CAAC;AAAA,IACjD;AAEA,QAAI;AACF,2BAAqB;AAAA,IACvB,SAAS,sBAAsB;AAW7B,UAAI,OAAO,eAAe,UAAU;AAClC,mBAAW,qBAAqB;AAAA,MAClC,OAAO;AACL,iBAAS,KAAK,wBAAwB,EAAE,OAAO;AAAA,MACjD;AAAA,IACF;AAAA;AAAA;;;ACxvBA;AAAA;AAAA,WAAO,UAAU,CAAC,QAAQ,QACxB,GAAG,MAAM,IAAI,GAAG,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,GAAG,CAAC,CAAC;AAAA;AAAA;;;ACD5D;AAAA;AAAA,QAAM,QAAQ;AAEd,QAAI,aAAa;AAEjB,WAAO,UAAU,CAAC;AAAA,MAChB,IAAI;AAAA,MACJ;AAAA,MACA,UAAU,CAAC;AAAA,IACb,MAAM;AACJ,UAAI,KAAK;AACT,UAAI,OAAO,OAAO,aAAa;AAC7B,aAAK,MAAM,OAAO,UAAU;AAC5B,sBAAc;AAAA,MAChB;AAEA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACpBA;AAAA;AAAA,QAAI,UAAU;AAEd,YAAQ,UAAU;AAElB,YAAQ,aAAa,CAAC,aAAa;AACjC,gBAAU;AAAA,IACZ;AAEA,YAAQ,MAAM,IAAI,SAAU,UAAU,QAAQ,IAAI,MAAM,SAAM,IAAI,IAAI;AAAA;AAAA;;;ACRtE;AAAA;AAAA,QAAM,YAAY;AAClB,QAAM,EAAE,IAAI,IAAI;AAChB,QAAM,QAAQ;AAEd,QAAI,mBAAmB;AAEvB,WAAO,UAAU,MAAM;AACrB,YAAM,KAAK,MAAM,aAAa,gBAAgB;AAC9C,YAAM,UAAU,CAAC;AACjB,YAAM,iBAAiB,CAAC;AACxB,UAAI,WAAW,CAAC;AAEhB,0BAAoB;AAEpB,YAAM,cAAc,MAAM,SAAS;AACnC,YAAM,gBAAgB,MAAM,OAAO,KAAK,OAAO,EAAE;AAEjD,YAAM,UAAU,MAAM;AACpB,YAAI,SAAS,WAAW,GAAG;AACzB,gBAAM,OAAO,OAAO,KAAK,OAAO;AAChC,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,gBAAI,OAAO,eAAe,KAAK,CAAC,CAAC,MAAM,aAAa;AAClD,uBAAS,CAAC,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC;AAC5B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,QAAQ,CAAC,QAAQ,YACrB,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC/B,cAAM,MAAM,UAAU,EAAE,QAAQ,QAAQ,CAAC;AACzC,iBAAS,KAAK,OAAO,MAAM;AACzB,mBAAS,MAAM;AACf,yBAAe,EAAE,EAAE,IAAI;AACvB,cAAI;AACF,oBAAQ,MAAM,EAAE,MAAM,EAAE,MAAM,SAAM,CAAC,GAAG,SAAS,IAAI,EAAE,CAAC,CAAC;AAAA,UAC3D,SAAS,KAAK;AACZ,mBAAO,GAAG;AAAA,UACZ,UAAE;AACA,mBAAO,eAAe,EAAE,EAAE;AAC1B,oBAAQ;AAAA,UACV;AAAA,QACF,CAAC;AACD,YAAI,IAAI,EAAE,UAAU,IAAI,EAAE,cAAc;AACxC,YAAI,IAAI,EAAE,sBAAsB,SAAS,MAAM,EAAE;AACjD,gBAAQ;AAAA,MACV,CAAC;AAGH,YAAM,YAAY,CAAC,MAAM;AACvB,gBAAQ,EAAE,EAAE,IAAI;AAChB,YAAI,IAAI,EAAE,UAAU,EAAE,EAAE,EAAE;AAC1B,YAAI,IAAI,EAAE,wBAAwB,cAAc,CAAC,EAAE;AACnD,gBAAQ;AACR,eAAO,EAAE;AAAA,MACX;AAEA,YAAM,SAAS,OAAO,WAAW,YAAY;AAC3C,YAAI,cAAc,MAAM,GAAG;AACzB,gBAAM,MAAM,IAAI,EAAE,4DAA4D;AAAA,QAChF;AACA,eAAO,MAAM,QAAQ,OAAO;AAAA,MAC9B;AAEA,YAAM,YAAY,YAAY;AAC5B,eAAO,KAAK,OAAO,EAAE,QAAQ,OAAO,QAAQ;AAC1C,gBAAM,QAAQ,GAAG,EAAE,UAAU;AAAA,QAC/B,CAAC;AACD,mBAAW,CAAC;AAAA,MACd;AAEA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AC/EA;AAAA;AACA,aAAS,aAAa;AAElB,UAAI,OAAO,WAAW,eAAe,OAAO,OAAO,YAAY,YAAY,OAAO,QAAQ,SAAS,YAAY;AAC3G,eAAO;AAAA,MACX;AAGA,UAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,aAAa,YAAY,CAAC,CAAC,QAAQ,SAAS,UAAU;AACvG,eAAO;AAAA,MACX;AAGA,UAAI,OAAO,cAAc,YAAY,OAAO,UAAU,cAAc,YAAY,UAAU,UAAU,QAAQ,UAAU,KAAK,GAAG;AAC1H,eAAO;AAAA,MACX;AAEA,aAAO;AAAA,IACX;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpBjB;AAAA;AAAA,QAAM,aAAa;AAEnB,WAAO,UAAU,CAAC,QAAQ;AACxB,YAAM,MAAM,CAAC;AAEb,UAAI,OAAO,sBAAsB,aAAa;AAC5C,YAAI,OAAO;AAAA,MACb,WAAW,WAAW,GAAG;AACvB,YAAI,OAAO;AAAA,MACb,WAAW,OAAO,aAAa,UAAU;AACvC,YAAI,OAAO;AAAA,MACb,WAAW,OAAO,YAAY,YAAY,OAAO,cAAY,YAAY;AACvE,YAAI,OAAO;AAAA,MACb;AAEA,UAAI,OAAO,QAAQ,aAAa;AAC9B,eAAO;AAAA,MACT;AAEA,aAAO,IAAI,GAAG;AAAA,IAChB;AAAA;AAAA;;;ACpBA;AAAA;AAAA,QAAM,YAAY,yBAA4B,MAAM,MAAM;AAE1D,QAAM,aAAa,YAAY,OAAM,IAAI,IAAI,GAAG,OAAO,SAAS,IAAI,EAAG,OAAO,OAAK;AAEnF,WAAO,UAAU,CAAC,YAAY;AAC5B,YAAM,OAAO,EAAE,GAAG,QAAQ;AAC1B,OAAC,YAAY,cAAc,UAAU,EAAE,QAAQ,CAAC,QAAQ;AACtD,YAAI,QAAQ,GAAG,GAAG;AAChB,eAAK,GAAG,IAAI,WAAW,KAAK,GAAG,CAAC;AAAA,QAClC;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAAA;AAAA;;;ACZA;AAAA;AAiBA,WAAO,UAAU,CAAC,SAAS;AACzB,YAAM,SAAS,CAAC;AAChB,YAAM,aAAa,CAAC;AACpB,YAAM,QAAQ,CAAC;AACf,YAAM,QAAQ,CAAC;AACf,YAAM,UAAU,CAAC;AAEjB,UAAI,KAAK,QAAQ;AACf,aAAK,OAAO,QAAQ,CAAC,UAAU;AAC7B,gBAAM,WAAW,QAAQ,CAAC,cAAc;AACtC,sBAAU,MAAM,QAAQ,CAAC,SAAS;AAChC,mBAAK,MAAM,QAAQ,CAAC,SAAS;AAC3B,qBAAK,QAAQ,QAAQ,CAAC,QAAQ;AAC5B,0BAAQ,KAAK;AAAA,oBACX,GAAG;AAAA,oBAAK;AAAA,oBAAM;AAAA,oBAAO;AAAA,oBAAW;AAAA,oBAAM;AAAA,kBACxC,CAAC;AAAA,gBACH,CAAC;AACD,sBAAM,KAAK;AAAA,kBACT,GAAG;AAAA,kBAAM;AAAA,kBAAM;AAAA,kBAAO;AAAA,kBAAW;AAAA,gBACnC,CAAC;AAAA,cACH,CAAC;AACD,oBAAM,KAAK;AAAA,gBACT,GAAG;AAAA,gBAAM;AAAA,gBAAM;AAAA,gBAAO;AAAA,cACxB,CAAC;AAAA,YACH,CAAC;AACD,uBAAW,KAAK;AAAA,cACd,GAAG;AAAA,cAAW;AAAA,cAAM;AAAA,YACtB,CAAC;AAAA,UACH,CAAC;AACD,iBAAO,KAAK;AAAA,YACV,GAAG;AAAA,YAAO;AAAA,UACZ,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,QACL,GAAG;AAAA,QAAM;AAAA,QAAQ;AAAA,QAAY;AAAA,QAAO;AAAA,QAAO;AAAA,MAC7C;AAAA,IACF;AAAA;AAAA;;;ACvDA;AAAA;AAMA,WAAO,UAAU;AAAA,MACf,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,yBAAyB;AAAA,MACzB,SAAS;AAAA,IACX;AAAA;AAAA;;;ACXA;AAAA;AAAA;AAAA,MACE,MAAQ;AAAA,MACR,SAAW;AAAA,MACX,aAAe;AAAA,MACf,MAAQ;AAAA,MACR,OAAS;AAAA,MACT,OAAS;AAAA,MACT,UAAY;AAAA,MACZ,SAAW;AAAA,QACT,OAAS;AAAA,QACT,OAAS;AAAA,QACT,qBAAqB;AAAA,QACrB,kBAAkB;AAAA,QAClB,gBAAkB;AAAA,QAClB,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,oBAAoB;AAAA,QACpB,uBAAuB;AAAA,QACvB,0BAA0B;AAAA,QAC1B,0BAA0B;AAAA,QAC1B,mBAAmB;AAAA,QACnB,MAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAe;AAAA,MACjB;AAAA,MACA,SAAW;AAAA,QACT,8BAA8B;AAAA,MAChC;AAAA,MACA,QAAU;AAAA,MACV,cAAgB;AAAA,QACd;AAAA,MACF;AAAA,MACA,SAAW;AAAA,MACX,iBAAmB;AAAA,QACjB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,qBAAqB;AAAA,QACrB,2BAA2B;AAAA,QAC3B,OAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,QAAU;AAAA,QACV,MAAQ;AAAA,QACR,QAAU;AAAA,QACV,6BAA6B;AAAA,QAC7B,wBAAwB;AAAA,QACxB,aAAa;AAAA,QACb,SAAW;AAAA,QACX,OAAS;AAAA,QACT,yBAAyB;AAAA,QACzB,eAAe;AAAA,QACf,KAAO;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,WAAW;AAAA,QACX,SAAW;AAAA,QACX,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,0BAA0B;AAAA,QAC1B,4BAA4B;AAAA,MAC9B;AAAA,MACA,cAAgB;AAAA,QACd,UAAU;AAAA,QACV,cAAc;AAAA,QACd,eAAe;AAAA,QACf,UAAU;AAAA,QACV,cAAc;AAAA,QACd,8BAA8B;AAAA,QAC9B,uBAAuB;AAAA,QACvB,qBAAqB;AAAA,QACrB,uBAAuB;AAAA,QACvB,QAAU;AAAA,MACZ;AAAA,MACA,WAAa;AAAA,QACX,uBAAuB;AAAA,MACzB;AAAA,MACA,YAAc;AAAA,QACZ,MAAQ;AAAA,QACR,KAAO;AAAA,MACT;AAAA,MACA,MAAQ;AAAA,QACN,KAAO;AAAA,MACT;AAAA,MACA,UAAY;AAAA,MACZ,YAAc;AAAA,QACZ,MAAQ;AAAA,QACR,KAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;AC1FA;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMf,eAAe;AAAA,MACf,QAAQ,MAAM;AAAA,MAAC;AAAA,IACjB;AAAA;AAAA;;;ACRA,IAAAI,0BAAA;AAAA;AAAA,QAAM,UAAU,kBAAiC;AACjD,QAAM,iBAAiB;AAKvB,WAAO,UAAU;AAAA,MACf,GAAG;AAAA,MACH,YAAY,8CAA8C,OAAO;AAAA,IACnE;AAAA;AAAA;;;ACTA;AAAA;AAOA,WAAO,UAAU,CAAC,EAAE,YAAY,cAAc,MAAM;AAClD,UAAI;AACJ,UAAI,QAAQ,OAAO,eAAe;AAChC,cAAM,OAAO,IAAI,KAAK,CAAC,kBAAkB,UAAU,KAAK,GAAG;AAAA,UACzD,MAAM;AAAA,QACR,CAAC;AACD,iBAAS,IAAI,OAAO,IAAI,gBAAgB,IAAI,CAAC;AAAA,MAC/C,OAAO;AACL,iBAAS,IAAI,OAAO,UAAU;AAAA,MAChC;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACnBA;AAAA;AAOA,WAAO,UAAU,CAAC,WAAW;AAC3B,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACTA;AAAA;AAAA,WAAO,UAAU,CAAC,QAAQ,YAAY;AACpC,aAAO,YAAY,CAAC,EAAE,KAAK,MAAM;AAC/B,gBAAQ,IAAI;AAAA,MACd;AAAA,IACF;AAAA;AAAA;;;ACJA;AAAA;AAOA,WAAO,UAAU,OAAO,QAAQ,WAAW;AACzC,aAAO,YAAY,MAAM;AAAA,IAC3B;AAAA;AAAA;;;ACTA;AAAA;AAOA,QAAM,qBAAqB,CAAC,SAC1B,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC/B,YAAM,aAAa,IAAI,WAAW;AAClC,iBAAW,SAAS,MAAM;AACxB,gBAAQ,WAAW,MAAM;AAAA,MAC3B;AACA,iBAAW,UAAU,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,MAAM;AACxD,eAAO,MAAM,gCAAgC,IAAI,EAAE,CAAC;AAAA,MACtD;AACA,iBAAW,kBAAkB,IAAI;AAAA,IACnC,CAAC;AAUH,QAAM,YAAY,OAAO,UAAU;AACjC,UAAI,OAAO;AACX,UAAI,OAAO,UAAU,aAAa;AAChC,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,UAAU,UAAU;AAE7B,YAAI,yCAAyC,KAAK,KAAK,GAAG;AACxD,iBAAO,KAAK,MAAM,MAAM,GAAG,EAAE,CAAC,CAAC,EAC5B,MAAM,EAAE,EACR,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAAA,QAC/B,OAAO;AACL,gBAAM,OAAO,MAAM,MAAM,KAAK;AAC9B,iBAAO,MAAM,KAAK,YAAY;AAAA,QAChC;AAAA,MACF,WAAW,OAAO,gBAAgB,eAAe,iBAAiB,aAAa;AAC7E,YAAI,MAAM,YAAY,OAAO;AAC3B,iBAAO,MAAM,UAAU,MAAM,GAAG;AAAA,QAClC;AACA,YAAI,MAAM,YAAY,SAAS;AAC7B,iBAAO,MAAM,UAAU,MAAM,MAAM;AAAA,QACrC;AACA,YAAI,MAAM,YAAY,UAAU;AAC9B,gBAAM,IAAI,QAAQ,CAAC,YAAY;AAC7B,kBAAM,OAAO,OAAO,SAAS;AAC3B,qBAAO,MAAM,mBAAmB,IAAI;AACpC,sBAAQ;AAAA,YACV,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,WAAW,OAAO,oBAAoB,eAAe,iBAAiB,iBAAiB;AACrF,cAAM,OAAO,MAAM,MAAM,cAAc;AACvC,eAAO,MAAM,mBAAmB,IAAI;AAAA,MACtC,WAAW,iBAAiB,QAAQ,iBAAiB,MAAM;AACzD,eAAO,MAAM,mBAAmB,KAAK;AAAA,MACvC;AAEA,aAAO,IAAI,WAAW,IAAI;AAAA,IAC5B;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpEjB;AAAA;AASA,QAAM,iBAAiB;AACvB,QAAM,cAAc;AACpB,QAAM,kBAAkB;AACxB,QAAM,YAAY;AAClB,QAAM,OAAO;AACb,QAAM,YAAY;AAElB,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;ACvBA;AAAA;AAAA,QAAM,eAAe;AACrB,QAAM,cAAc;AACpB,QAAM,YAAY;AAClB,QAAM,EAAE,IAAI,IAAI;AAChB,QAAM,QAAQ;AACd,QAAM,MAAM;AACZ,QAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,QAAI,gBAAgB;AAEpB,WAAO,UAAU,OAAO,QAAQ,OAAO,MAAM,IAAI,WAAW,WAAW,CAAC,GAAG,SAAS,CAAC,MAAM;AACzF,YAAM,KAAK,MAAM,UAAU,aAAa;AACxC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA,GAAG;AAAA,MACL,IAAI,aAAa;AAAA,QACf,GAAG;AAAA,QACH,GAAG;AAAA,MACL,CAAC;AACD,YAAM,WAAW,CAAC;AAClB,YAAM,UAAU,CAAC;AAIjB,YAAM,eAAe,OAAO,UAAU,WAAW,MAAM,MAAM,GAAG,IAAI;AACpE,UAAI,aAAa;AACjB,UAAI,gBAAgB;AACpB,YAAM,eAAe,CAAC,IAAI,SAAS,IAAI,SAAS,EAAE,SAAS,GAAG,KAAK,CAAC,QAAQ;AAE5E,UAAI;AACJ,UAAI;AACJ,YAAM,YAAY,IAAI,QAAQ,CAAC,SAAS,WAAW;AACjD,2BAAmB;AACnB,0BAAkB;AAAA,MACpB,CAAC;AACD,YAAM,cAAc,CAAC,UAAU;AAAE,wBAAgB,MAAM,OAAO;AAAA,MAAG;AAEjE,UAAI,SAAS,YAAY,OAAO;AAChC,aAAO,UAAU;AAEjB,uBAAiB;AAEjB,YAAM,aAAa,CAAC,WAAW,QAAQ;AACrC,iBAAS,SAAS,IAAI;AAAA,MACxB;AAEA,YAAM,YAAY,CAAC,WAAW,QAAQ;AACpC,gBAAQ,SAAS,IAAI;AAAA,MACvB;AAEA,YAAM,WAAW,CAAC,EAAE,IAAI,OAAO,QAAQ,QAAQ,MAC7C,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC/B,YAAI,IAAI,EAAE,YAAY,KAAK,YAAY,MAAM,EAAE;AAE/C,cAAM,YAAY,GAAG,MAAM,IAAI,KAAK;AACpC,mBAAW,WAAW,OAAO;AAC7B,kBAAU,WAAW,MAAM;AAC3B,aAAK,QAAQ;AAAA,UACX,UAAU;AAAA,UACV;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAGH,YAAM,OAAO,MACX,QAAQ,KAAK,qFAAqF;AAGpG,YAAM,eAAe,CAAC,UACpB,SAAS,UAAU;AAAA,QACjB,IAAI;AAAA,QAAO,QAAQ;AAAA,QAAQ,SAAS,EAAE,SAAS,EAAE,UAAU,cAAc,UAAU,QAAQ,UAAU,SAAS,QAAQ,QAAQ,EAAE;AAAA,MAClI,CAAC,CAAC;AAGJ,YAAM,YAAY,CAAC,MAAM,MAAM,UAC7B,SAAS,UAAU;AAAA,QACjB,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS,EAAE,QAAQ,aAAa,MAAM,CAAC,MAAM,IAAI,EAAE;AAAA,MACrD,CAAC,CAAC;AAGJ,YAAM,WAAW,CAAC,MAAM,UACtB,SAAS,UAAU;AAAA,QACjB,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS,EAAE,QAAQ,YAAY,MAAM,CAAC,MAAM,EAAE,UAAU,OAAO,CAAC,EAAE;AAAA,MACpE,CAAC,CAAC;AAGJ,YAAM,aAAa,CAAC,MAAM,UACxB,SAAS,UAAU;AAAA,QACjB,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS,EAAE,QAAQ,UAAU,MAAM,CAAC,IAAI,EAAE;AAAA,MAC5C,CAAC,CAAC;AAGJ,YAAM,KAAK,CAAC,QAAQ,MAAM,UACxB,SAAS,UAAU;AAAA,QACjB,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS,EAAE,QAAQ,KAAK;AAAA,MAC1B,CAAC,CAAC;AAGJ,YAAM,eAAe,MACnB,QAAQ,KAAK,2GAA2G;AAG1H,YAAM,uBAAuB,CAAC,QAAQ,UAAU,SAAS,UAAU;AAAA,QACjE,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,OAAO;AAAA,UACP,SAAS;AAAA,YACP,UAAU,QAAQ;AAAA,YAClB,UAAU,QAAQ;AAAA,YAClB,WAAW,QAAQ;AAAA,YACnB,aAAa,QAAQ;AAAA,YACrB,MAAM,QAAQ;AAAA,YACd,UAAU,CAAC,IAAI,SAAS,IAAI,SAAS,EAAE,SAAS,UAAU,KACrD,CAAC,QAAQ;AAAA,UAChB;AAAA,QACF;AAAA,MACF,CAAC,CAAC;AAEF,YAAM,aAAa,MACjB,QAAQ,KAAK,gGAAgG;AAG/G,YAAM,qBAAqB,CAAC,QAAQ,MAAM,SAAS,UACjD,SAAS,UAAU;AAAA,QACjB,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS,EAAE,OAAO,QAAQ,KAAK,MAAM,QAAQ,QAAQ;AAAA,MACvD,CAAC,CAAC;AAGJ,YAAM,eAAe,CAACC,SAAQ,OAAOC,MAAKC,SAAQ,UAAU;AAE1D,YAAI,gBAAgB,CAAC,IAAI,gBAAgB,IAAI,uBAAuB,EAAE,SAASD,IAAG,EAAG,OAAM,MAAM,0CAA0C;AAE3I,cAAM,OAAOA,QAAO;AACpB,qBAAa;AAEb,cAAM,UAAUC,WAAU;AAC1B,wBAAgB;AAOhB,cAAM,WAAW,OAAOF,WAAU,WAAWA,OAAM,MAAM,GAAG,IAAIA;AAChE,cAAM,SAAS,SAAS,OAAO,CAAC,MAAM,CAAC,aAAa,SAAS,CAAC,CAAC;AAC/D,qBAAa,KAAK,GAAG,MAAM;AAE3B,YAAI,OAAO,SAAS,GAAG;AACrB,iBAAO,qBAAqB,QAAQ,KAAK,EACtC,KAAK,MAAM,mBAAmBA,QAAO,MAAM,SAAS,KAAK,CAAC;AAAA,QAC/D;AAEA,eAAO,mBAAmBA,QAAO,MAAM,SAAS,KAAK;AAAA,MACvD;AAEA,YAAM,gBAAgB,CAAC,SAAS,CAAC,GAAG,UAClC,SAAS,UAAU;AAAA,QACjB,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS,EAAE,OAAO;AAAA,MACpB,CAAC,CAAC;AAGJ,YAAM,YAAY,OAAO,OAAO,OAAO,CAAC,GAAG,SAAS;AAAA,QAClD,QAAQ;AAAA,QAAM,MAAM;AAAA,QAAM,MAAM;AAAA,QAAM,KAAK;AAAA,MAC7C,GAAG,UACD,SAAS,UAAU;AAAA,QACjB,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS,EAAE,OAAO,MAAM,UAAU,KAAK,GAAG,SAAS,MAAM,OAAO;AAAA,MAClE,CAAC,CAAC;AAGJ,YAAM,SAAS,CAAC,QAAQ,wBAAwB,WAAW,OAAO,UAAU;AAC1E,gBAAQ,IAAI,wFAAwF;AACpG,eAAO,SAAS,UAAU;AAAA,UACxB,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,SAAS,EAAE,OAAO,SAAS;AAAA,QAC7B,CAAC,CAAC;AAAA,MACJ;AAEA,YAAM,SAAS,OAAO,OAAO,UAAU;AACrC,YAAI,aAAc,OAAM,MAAM,8DAA8D;AAE5F,eAAO,SAAS,UAAU;AAAA,UACxB,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,SAAS,EAAE,OAAO,MAAM,UAAU,KAAK,EAAE;AAAA,QAC3C,CAAC,CAAC;AAAA,MACJ;AAEA,YAAM,YAAY,YAAY;AAC5B,YAAI,WAAW,MAAM;AAOnB,0BAAgB,MAAM;AACtB,mBAAS;AAAA,QACX;AACA,eAAO,QAAQ,QAAQ;AAAA,MACzB;AAEA,gBAAU,QAAQ,CAAC;AAAA,QACjB;AAAA,QAAU;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAQ;AAAA,MACnC,MAAM;AACJ,cAAM,YAAY,GAAG,MAAM,IAAI,KAAK;AACpC,YAAI,WAAW,WAAW;AACxB,cAAI,IAAI,QAAQ,eAAe,KAAK,EAAE;AACtC,cAAI,IAAI;AACR,cAAI,WAAW,aAAa;AAC1B,gBAAI,YAAY,IAAI;AAAA,UACtB,WAAW,WAAW,UAAU;AAC9B,gBAAI,MAAM,KAAK,EAAE,GAAG,MAAM,QAAQ,OAAO,KAAK,IAAI,EAAE,OAAO,CAAC;AAAA,UAC9D;AACA,mBAAS,SAAS,EAAE,EAAE,OAAO,MAAM,EAAE,CAAC;AAAA,QACxC,WAAW,WAAW,UAAU;AAC9B,kBAAQ,SAAS,EAAE,IAAI;AACvB,cAAI,WAAW,OAAQ,iBAAgB,IAAI;AAC3C,cAAI,cAAc;AAChB,yBAAa,IAAI;AAAA,UACnB,OAAO;AACL,kBAAM,MAAM,IAAI;AAAA,UAClB;AAAA,QACF,WAAW,WAAW,YAAY;AAChC,iBAAO,EAAE,GAAG,MAAM,WAAW,MAAM,CAAC;AAAA,QACtC;AAAA,MACF,CAAC;AAED,YAAM,aAAa;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,mBAAa,EACV,KAAK,MAAM,qBAAqB,KAAK,CAAC,EACtC,KAAK,MAAM,mBAAmB,OAAO,KAAK,MAAM,CAAC,EACjD,KAAK,MAAM,iBAAiB,UAAU,CAAC,EACvC,MAAM,MAAM;AAAA,MAAC,CAAC;AAEjB,aAAO;AAAA,IACT;AAAA;AAAA;;;ACxRA;AAAA;AAAA,QAAM,eAAe;AAErB,QAAM,YAAY,OAAO,OAAO,OAAO,YAAY;AACjD,YAAM,SAAS,MAAM,aAAa,OAAO,GAAG,OAAO;AACnD,aAAO,OAAO,UAAU,KAAK,EAC1B,QAAQ,YAAY;AACnB,cAAM,OAAO,UAAU;AAAA,MACzB,CAAC;AAAA,IACL;AAEA,QAAM,SAAS,OAAO,OAAO,YAAY;AACvC,YAAM,SAAS,MAAM,aAAa,OAAO,GAAG,OAAO;AACnD,aAAO,OAAO,OAAO,KAAK,EACvB,QAAQ,YAAY;AACnB,cAAM,OAAO,UAAU;AAAA,MACzB,CAAC;AAAA,IACL;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;ACrBA;AAAA;AAkHA,WAAO,UAAU;AAAA,MACf,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,UAAU;AAAA,MACV,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,SAAS;AAAA,MACT,SAAS;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,SAAS;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,SAAS;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,SAAS;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,UAAU;AAAA,MACV,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,UAAU;AAAA,MACV,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA;AAAA;;;ACzNA;AAAA;AAGA,WAAO,UAAU;AAAA,MACf,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,MACX,MAAM;AAAA,MACN,eAAe;AAAA,MACf,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,aAAa;AAAA,MACb,aAAa;AAAA,MACb,aAAa;AAAA,MACb,aAAa;AAAA,MACb,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,UAAU;AAAA,IACZ;AAAA;AAAA;;;AClBA;AAAA;AASA;AACA,QAAM,kBAAkB;AACxB,QAAM,eAAe;AACrB,QAAM,YAAY;AAClB,QAAM,YAAY;AAClB,QAAM,MAAM;AACZ,QAAM,MAAM;AACZ,QAAM,EAAE,WAAW,IAAI;AAEvB,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL;AAAA;AAAA;", "names": ["exports", "value", "key", "next", "require_defaultOptions", "langs", "oem", "config"]}