import { showToast } from "./toast";

// Frontend-only mode - using mock data instead of backend API
console.log("🎯 Running in frontend-only mode with mock data");
console.log("Environment:", import.meta.env.MODE);

// Simulate API delay for realistic experience
const simulateDelay = (ms = 500) =>
  new Promise((resolve) => setTimeout(resolve, ms));

// Generate unique IDs for new items
const generateId = () =>
  `id_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// Mock data storage - using localStorage for persistence
const STORAGE_KEYS = {
  rooms: "pg_mock_rooms",
  tenants: "pg_mock_tenants",
  rents: "pg_mock_rents",
  admin: "pg_mock_admin",
};

// Initialize mock data
const initializeMockData = () => {
  // Default rooms data
  const defaultRooms = [
    {
      _id: "room1",
      roomNumber: "101",
      floorNumber: 1,
      capacity: 2,
      rentAmount: 5000,
      occupiedBeds: 1,
      isOccupied: false,
      adminId: "admin1",
      amenities: ["WiFi", "AC", "Attached Bathroom"],
      createdAt: new Date().toISOString(),
    },
    {
      _id: "room2",
      roomNumber: "102",
      floorNumber: 1,
      capacity: 2,
      rentAmount: 5500,
      occupiedBeds: 1,
      isOccupied: false,
      adminId: "admin1",
      amenities: ["WiFi", "AC", "Balcony"],
      createdAt: new Date().toISOString(),
    },
    {
      _id: "room3",
      roomNumber: "201",
      floorNumber: 2,
      capacity: 3,
      rentAmount: 4500,
      occupiedBeds: 2,
      isOccupied: false,
      adminId: "admin1",
      amenities: ["WiFi", "Fan"],
      createdAt: new Date().toISOString(),
    },
  ];

  // Default tenants data
  const defaultTenants = [
    {
      _id: "tenant1",
      name: "John Doe",
      phone: "1234567890",
      email: "<EMAIL>",
      roomId: {
        _id: "room1",
        roomNumber: "101",
        floorNumber: 1,
        capacity: 2,
        rentAmount: 5000,
      },
      bedNumber: 1,
      active: true,
      joiningDate: new Date().toISOString(),
      adminId: "admin1",
      idProofType: "Aadhar",
      occupation: "Software Engineer",
      emergencyContact: "9876543210",
      address: "123 Main St, City",
    },
    {
      _id: "tenant2",
      name: "Jane Smith",
      phone: "0987654321",
      email: "<EMAIL>",
      roomId: {
        _id: "room2",
        roomNumber: "102",
        floorNumber: 1,
        capacity: 2,
        rentAmount: 5500,
      },
      bedNumber: 1,
      active: true,
      joiningDate: new Date().toISOString(),
      adminId: "admin1",
      idProofType: "PAN",
      occupation: "Designer",
      emergencyContact: "8765432109",
      address: "456 Oak Ave, City",
    },
  ];

  // Default rents data
  const defaultRents = [
    {
      _id: "rent1",
      tenantId: "tenant1",
      roomId: "room1",
      amount: 5000,
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
      isPaid: true,
      paidDate: new Date().toISOString(),
      dueDate: new Date(new Date().setDate(5)).toISOString(),
    },
    {
      _id: "rent2",
      tenantId: "tenant2",
      roomId: "room2",
      amount: 5500,
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
      isPaid: false,
      dueDate: new Date(new Date().setDate(5)).toISOString(),
    },
  ];

  // Initialize data if not exists
  if (!localStorage.getItem(STORAGE_KEYS.rooms)) {
    localStorage.setItem(STORAGE_KEYS.rooms, JSON.stringify(defaultRooms));
  }
  if (!localStorage.getItem(STORAGE_KEYS.tenants)) {
    localStorage.setItem(STORAGE_KEYS.tenants, JSON.stringify(defaultTenants));
  }
  if (!localStorage.getItem(STORAGE_KEYS.rents)) {
    localStorage.setItem(STORAGE_KEYS.rents, JSON.stringify(defaultRents));
  }
};

// Initialize mock data on load
initializeMockData();

// Helper functions for localStorage operations
const getStoredData = (key) => {
  try {
    const data = localStorage.getItem(key);
    return data ? JSON.parse(data) : [];
  } catch (error) {
    console.error(`Error reading ${key} from localStorage:`, error);
    return [];
  }
};

const setStoredData = (key, data) => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error(`Error saving ${key} to localStorage:`, error);
  }
};

// Mock API calls for rooms, tenants, and rents

// Room API calls
export const getRooms = async (params = {}) => {
  await simulateDelay();
  console.log("🏠 Fetching rooms with params:", params);

  const rooms = getStoredData(STORAGE_KEYS.rooms);

  return {
    data: {
      success: true,
      count: rooms.length,
      data: rooms,
    },
  };
};

export const getRoom = async (id) => {
  await simulateDelay();
  console.log("🏠 Fetching room with id:", id);

  const rooms = getStoredData(STORAGE_KEYS.rooms);
  const room = rooms.find((r) => r._id === id);

  if (!room) {
    throw new Error(`Room with id ${id} not found`);
  }

  return {
    data: {
      success: true,
      data: room,
    },
  };
};

export const createRoom = async (roomData) => {
  await simulateDelay();
  console.log("🏠 Creating room with data:", roomData);

  const rooms = getStoredData(STORAGE_KEYS.rooms);
  const newRoom = {
    ...roomData,
    _id: generateId(),
    adminId: "admin1",
    occupiedBeds: 0,
    isOccupied: false,
    createdAt: new Date().toISOString(),
  };

  rooms.push(newRoom);
  setStoredData(STORAGE_KEYS.rooms, rooms);

  showToast("Room created successfully", { type: "success" });

  return {
    data: {
      success: true,
      data: newRoom,
    },
  };
};

export const updateRoom = async (id, roomData) => {
  await simulateDelay();
  console.log("🏠 Updating room with id:", id, "data:", roomData);

  const rooms = getStoredData(STORAGE_KEYS.rooms);
  const roomIndex = rooms.findIndex((r) => r._id === id);

  if (roomIndex === -1) {
    throw new Error(`Room with id ${id} not found`);
  }

  rooms[roomIndex] = {
    ...rooms[roomIndex],
    ...roomData,
    updatedAt: new Date().toISOString(),
  };

  setStoredData(STORAGE_KEYS.rooms, rooms);
  showToast("Room updated successfully", { type: "success" });

  return {
    data: {
      success: true,
      data: rooms[roomIndex],
    },
  };
};

export const deleteRoom = async (id) => {
  await simulateDelay();
  console.log("🏠 Deleting room with id:", id);

  const rooms = getStoredData(STORAGE_KEYS.rooms);
  const roomIndex = rooms.findIndex((r) => r._id === id);

  if (roomIndex === -1) {
    throw new Error(`Room with id ${id} not found`);
  }

  rooms.splice(roomIndex, 1);
  setStoredData(STORAGE_KEYS.rooms, rooms);

  showToast("Room deleted successfully", { type: "success" });

  return {
    data: {
      success: true,
      message: "Room deleted successfully",
    },
  };
};

// Tenant API calls
export const getTenants = async (params = {}) => {
  await simulateDelay();
  console.log("👥 Fetching tenants with params:", params);

  const tenants = getStoredData(STORAGE_KEYS.tenants);

  return {
    data: {
      success: true,
      count: tenants.length,
      data: tenants,
    },
  };
};

export const getTenant = async (id) => {
  await simulateDelay();
  console.log("👥 Fetching tenant with id:", id);

  const tenants = getStoredData(STORAGE_KEYS.tenants);
  const tenant = tenants.find((t) => t._id === id);

  if (!tenant) {
    throw new Error(`Tenant with id ${id} not found`);
  }

  return {
    data: {
      success: true,
      data: tenant,
    },
  };
};

export const createTenant = async (tenantData) => {
  await simulateDelay();
  console.log("👥 Creating tenant with data:", tenantData);

  const tenants = getStoredData(STORAGE_KEYS.tenants);
  const rooms = getStoredData(STORAGE_KEYS.rooms);

  // Find the room to get room details
  const room = rooms.find((r) => r._id === tenantData.roomId);
  if (!room) {
    throw new Error("Selected room not found");
  }

  const newTenant = {
    ...tenantData,
    _id: generateId(),
    adminId: "admin1",
    active: true,
    joiningDate: new Date().toISOString(),
    roomId: {
      _id: room._id,
      roomNumber: room.roomNumber,
      floorNumber: room.floorNumber,
      capacity: room.capacity,
      rentAmount: room.rentAmount,
    },
  };

  tenants.push(newTenant);
  setStoredData(STORAGE_KEYS.tenants, tenants);

  // Update room occupied beds
  const roomIndex = rooms.findIndex((r) => r._id === room._id);
  if (roomIndex !== -1) {
    rooms[roomIndex].occupiedBeds = (rooms[roomIndex].occupiedBeds || 0) + 1;
    rooms[roomIndex].isOccupied =
      rooms[roomIndex].occupiedBeds >= rooms[roomIndex].capacity;
    setStoredData(STORAGE_KEYS.rooms, rooms);
  }

  showToast("Tenant created successfully", { type: "success" });

  return {
    data: {
      success: true,
      data: newTenant,
    },
  };
};

export const updateTenant = async (id, tenantData) => {
  await simulateDelay();
  console.log("👥 Updating tenant with id:", id, "data:", tenantData);

  const tenants = getStoredData(STORAGE_KEYS.tenants);
  const tenantIndex = tenants.findIndex((t) => t._id === id);

  if (tenantIndex === -1) {
    throw new Error(`Tenant with id ${id} not found`);
  }

  tenants[tenantIndex] = {
    ...tenants[tenantIndex],
    ...tenantData,
    updatedAt: new Date().toISOString(),
  };

  setStoredData(STORAGE_KEYS.tenants, tenants);
  showToast("Tenant updated successfully", { type: "success" });

  return {
    data: {
      success: true,
      data: tenants[tenantIndex],
    },
  };
};

export const deleteTenant = async (id) => {
  await simulateDelay();
  console.log("👥 Deleting tenant with id:", id);

  const tenants = getStoredData(STORAGE_KEYS.tenants);
  const tenantIndex = tenants.findIndex((t) => t._id === id);

  if (tenantIndex === -1) {
    throw new Error(`Tenant with id ${id} not found`);
  }

  const tenant = tenants[tenantIndex];
  tenants.splice(tenantIndex, 1);
  setStoredData(STORAGE_KEYS.tenants, tenants);

  // Update room occupied beds
  const rooms = getStoredData(STORAGE_KEYS.rooms);
  const roomIndex = rooms.findIndex((r) => r._id === tenant.roomId._id);
  if (roomIndex !== -1) {
    rooms[roomIndex].occupiedBeds = Math.max(
      0,
      (rooms[roomIndex].occupiedBeds || 1) - 1
    );
    rooms[roomIndex].isOccupied =
      rooms[roomIndex].occupiedBeds >= rooms[roomIndex].capacity;
    setStoredData(STORAGE_KEYS.rooms, rooms);
  }

  showToast("Tenant deleted successfully", { type: "success" });

  return {
    data: {
      success: true,
      message: "Tenant deleted successfully",
    },
  };
};

// Rent API calls
export const getRents = async (params = {}) => {
  await simulateDelay();
  console.log("💰 Fetching rents with params:", params);

  const rents = getStoredData(STORAGE_KEYS.rents);

  return {
    data: {
      success: true,
      count: rents.length,
      data: rents,
    },
  };
};

export const getRent = async (id) => {
  await simulateDelay();
  console.log("💰 Fetching rent with id:", id);

  const rents = getStoredData(STORAGE_KEYS.rents);
  const rent = rents.find((r) => r._id === id);

  if (!rent) {
    throw new Error(`Rent with id ${id} not found`);
  }

  return {
    data: {
      success: true,
      data: rent,
    },
  };
};

export const createRent = async (rentData) => {
  await simulateDelay();
  console.log("💰 Creating rent with data:", rentData);

  const rents = getStoredData(STORAGE_KEYS.rents);
  const newRent = {
    ...rentData,
    _id: generateId(),
    createdAt: new Date().toISOString(),
  };

  rents.push(newRent);
  setStoredData(STORAGE_KEYS.rents, rents);

  showToast("Rent record created successfully", { type: "success" });

  return {
    data: {
      success: true,
      data: newRent,
    },
  };
};

export const updateRent = async (id, rentData) => {
  await simulateDelay();
  console.log("💰 Updating rent with id:", id, "data:", rentData);

  const rents = getStoredData(STORAGE_KEYS.rents);
  const rentIndex = rents.findIndex((r) => r._id === id);

  if (rentIndex === -1) {
    throw new Error(`Rent with id ${id} not found`);
  }

  rents[rentIndex] = {
    ...rents[rentIndex],
    ...rentData,
    updatedAt: new Date().toISOString(),
  };

  setStoredData(STORAGE_KEYS.rents, rents);
  showToast("Rent record updated successfully", { type: "success" });

  return {
    data: {
      success: true,
      data: rents[rentIndex],
    },
  };
};

export const deleteRent = async (id) => {
  await simulateDelay();
  console.log("💰 Deleting rent with id:", id);

  const rents = getStoredData(STORAGE_KEYS.rents);
  const rentIndex = rents.findIndex((r) => r._id === id);

  if (rentIndex === -1) {
    throw new Error(`Rent with id ${id} not found`);
  }

  rents.splice(rentIndex, 1);
  setStoredData(STORAGE_KEYS.rents, rents);

  showToast("Rent record deleted successfully", { type: "success" });

  return {
    data: {
      success: true,
      message: "Rent record deleted successfully",
    },
  };
};

// Dashboard API calls
export const getDashboardStats = async () => {
  await simulateDelay();
  console.log("📊 Fetching dashboard stats...");

  const rooms = getStoredData(STORAGE_KEYS.rooms);
  const tenants = getStoredData(STORAGE_KEYS.tenants);
  const rents = getStoredData(STORAGE_KEYS.rents);

  const totalRooms = rooms.length;
  const occupiedRooms = rooms.filter((room) => room.isOccupied).length;
  const totalTenants = tenants.filter((tenant) => tenant.active).length;
  const currentMonth = new Date().getMonth() + 1;
  const currentYear = new Date().getFullYear();
  const monthlyRents = rents.filter(
    (rent) => rent.month === currentMonth && rent.year === currentYear
  );
  const paidRents = monthlyRents.filter((rent) => rent.isPaid);
  const totalRentCollected = paidRents.reduce(
    (sum, rent) => sum + rent.amount,
    0
  );
  const pendingRents = monthlyRents.filter((rent) => !rent.isPaid);

  return {
    data: {
      success: true,
      data: {
        totalRooms,
        occupiedRooms,
        availableRooms: totalRooms - occupiedRooms,
        totalTenants,
        totalRentCollected,
        pendingRents: pendingRents.length,
        occupancyRate:
          totalRooms > 0 ? Math.round((occupiedRooms / totalRooms) * 100) : 0,
      },
    },
  };
};

// Auth API calls (mock)
export const loginAdmin = async (credentials) => {
  await simulateDelay();
  console.log("🔐 Admin login attempt:", credentials.email);

  // Simple mock authentication
  if (
    credentials.email === "<EMAIL>" &&
    credentials.password === "admin123"
  ) {
    const token = `mock_token_${Date.now()}`;
    localStorage.setItem("token", token);

    showToast("Login successful", { type: "success" });

    return {
      data: {
        success: true,
        token,
        admin: {
          _id: "admin1",
          name: "Admin User",
          email: "<EMAIL>",
        },
      },
    };
  } else {
    throw new Error("Invalid credentials");
  }
};

export const registerAdmin = async (adminData) => {
  await simulateDelay();
  console.log("📝 Admin registration:", adminData.email);

  const token = `mock_token_${Date.now()}`;
  localStorage.setItem("token", token);

  showToast("Registration successful", { type: "success" });

  return {
    data: {
      success: true,
      token,
      admin: {
        _id: generateId(),
        name: adminData.name,
        email: adminData.email,
      },
    },
  };
};

// No default export needed for frontend-only mode
export default null;
