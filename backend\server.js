const express = require("express");
const dotenv = require("dotenv");
const cors = require("cors");
const connectDB = require("./config/db");
const errorHandler = require("./middleware/error");
const {
  updateRentStatuses,
  generateMonthlyRents,
  checkForMissingRentRecords,
} = require("./utils/rentScheduler");

// Load env vars
dotenv.config();

console.log("🚀 Starting PG Management Server...");
console.log("📁 Current directory:", process.cwd());
console.log("🔧 Node version:", process.version);

// Connect to database
console.log("🔌 Attempting to connect to database...");
connectDB().catch(err => {
  console.error("❌ Database connection failed:", err.message);
  console.log("⚠️  Server will continue without database connection");
});

// Route files
const adminRoutes = require("./routes/adminRoutes");
const roomRoutes = require("./routes/roomRoutes");
const tenantRoutes = require("./routes/tenantRoutes");
const rentRoutes = require("./routes/rentRoutes");
const dashboardRoutes = require("./routes/dashboardRoutes");

const app = express();

// Body parser
app.use(express.json());

// Enable CORS
app.use(
  cors({
    origin: "*", // Allow all origins for now to fix the immediate issue
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

// Handle OPTIONS preflight requests manually
app.options("*", (req, res) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  res.header("Access-Control-Allow-Headers", "Content-Type, Authorization");
  res.status(200).send();
});

// Add CORS headers to all responses
app.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  res.header("Access-Control-Allow-Headers", "Content-Type, Authorization");
  next();
});

// Mount routers
app.use("/api/v1/admin", adminRoutes);
app.use("/api/v1/rooms", roomRoutes);
app.use("/api/v1/tenants", tenantRoutes);
app.use("/api/v1/rents", rentRoutes);
app.use("/api/v1/dashboard", dashboardRoutes);

// Basic route for testing
app.get("/", (req, res) => {
  res.send("PG Management System API is running...");
});

// Debug route
app.get("/api/v1/debug", (req, res) => {
  res.json({
    environment: process.env.NODE_ENV,
    cors: {
      origin: "*",
      methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
      allowedHeaders: ["Content-Type", "Authorization"],
    },
    headers: req.headers,
    requestOrigin: req.headers.origin || "No origin header",
    timestamp: new Date().toISOString(),
  });
});

// Error handler middleware
app.use(errorHandler);

const PORT = process.env.PORT || 5000;

const server = app.listen(PORT, () => {
  console.log("✅ Server running successfully!");
  console.log(`🌐 URL: http://localhost:${PORT}`);
  console.log(`🔗 API Base: http://localhost:${PORT}/api/v1`);
  console.log(`🧪 Test: http://localhost:${PORT}/api/v1/debug`);
  console.log(`🏠 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log("🎯 Ready to receive requests!");
});

// Handle server errors
server.on('error', (err) => {
  console.error("❌ Server error:", err.message);
  if (err.code === 'EADDRINUSE') {
    console.error(`❌ Port ${PORT} is already in use.`);
    console.log("💡 Try killing existing processes: taskkill /f /im node.exe");
    console.log("💡 Or use a different port by setting PORT environment variable");
    process.exit(1);
  }
});

// Set up scheduled tasks
// Run rent status update daily at midnight
const runDailyTasks = async () => {
  try {
    const now = new Date();
    console.log(`🕐 Running daily tasks at ${now.toISOString()}`);

    // Update rent statuses (mark as overdue if past due date)
    await updateRentStatuses();

    // Check for missing rent records for tenants who have paid their previous month's rent
    await checkForMissingRentRecords();

    // Check if it's the 25th day of the month to generate next month's rents
    if (now.getDate() === 25) {
      await generateMonthlyRents();
    }

    console.log("✅ Daily tasks completed successfully");
  } catch (error) {
    console.error("❌ Error running daily tasks:", error.message);
  }
};

// Run daily tasks after a delay to ensure database is connected
setTimeout(() => {
  console.log("🔄 Starting initial daily tasks...");
  runDailyTasks();
}, 5000);

// Then schedule to run daily
setInterval(runDailyTasks, 24 * 60 * 60 * 1000); // 24 hours

// Handle unhandled promise rejections
process.on("unhandledRejection", (err, promise) => {
  console.log(`Error: ${err.message}`);
  // Close server & exit process
  server.close(() => process.exit(1));
});
