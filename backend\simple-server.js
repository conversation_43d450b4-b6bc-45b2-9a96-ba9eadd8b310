console.log("Starting simple server...");

const express = require("express");
const cors = require("cors");

const app = express();

// Enable CORS
app.use(cors({
  origin: "*",
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"],
}));

app.use(express.json());

// Test route
app.get("/", (req, res) => {
  console.log("Root route hit");
  res.json({ message: "Simple server is running!" });
});

// Test API route
app.get("/api/v1/test", (req, res) => {
  console.log("Test API route hit");
  res.json({ success: true, message: "API is working!" });
});

// Mock rooms route
app.get("/api/v1/rooms", (req, res) => {
  console.log("Rooms route hit");
  res.json({
    success: true,
    count: 2,
    data: [
      {
        _id: "room1",
        roomNumber: "101",
        floorNumber: 1,
        capacity: 2,
        rentAmount: 5000,
        occupiedBeds: 0,
        isOccupied: false,
        adminId: "admin1"
      },
      {
        _id: "room2", 
        roomNumber: "102",
        floorNumber: 1,
        capacity: 2,
        rentAmount: 5500,
        occupiedBeds: 0,
        isOccupied: false,
        adminId: "admin1"
      }
    ]
  });
});

// Mock tenants route
app.get("/api/v1/tenants", (req, res) => {
  console.log("Tenants route hit");
  res.json({
    success: true,
    count: 2,
    data: [
      {
        _id: "tenant1",
        name: "Test Tenant 1",
        phone: "1234567890",
        email: "<EMAIL>",
        roomId: {
          _id: "room1",
          roomNumber: "101",
          floorNumber: 1,
          capacity: 2,
          rentAmount: 5000
        },
        bedNumber: 1,
        active: true,
        joiningDate: new Date().toISOString(),
        adminId: "admin1"
      },
      {
        _id: "tenant2",
        name: "Test Tenant 2", 
        phone: "0987654321",
        email: "<EMAIL>",
        roomId: {
          _id: "room2",
          roomNumber: "102",
          floorNumber: 1,
          capacity: 2,
          rentAmount: 5500
        },
        bedNumber: 1,
        active: true,
        joiningDate: new Date().toISOString(),
        adminId: "admin1"
      }
    ]
  });
});

// Mock last-updated route
app.get("/api/v1/tenants/last-updated", (req, res) => {
  console.log("Last updated route hit");
  res.json({
    success: true,
    lastUpdated: new Date().toISOString()
  });
});

const PORT = 5000;

app.listen(PORT, () => {
  console.log(`✅ Simple server running on http://localhost:${PORT}`);
  console.log(`✅ Test it: http://localhost:${PORT}/api/v1/test`);
});

// Handle errors
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
});

process.on('unhandledRejection', (err) => {
  console.error('Unhandled Rejection:', err);
});
