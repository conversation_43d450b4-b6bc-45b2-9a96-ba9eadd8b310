# PG Hostel Management System - Frontend Only

A React-based frontend application for managing PG (Paying Guest) hostels. This system allows PG admins to manage their rooms, tenants, and rent payments using mock data stored in localStorage.

## Project Structure

This is a frontend-only application that runs completely in the browser without requiring a backend server. All data is stored locally using localStorage for persistence.

## Features

- Mock admin authentication (demo credentials: <EMAIL> / admin123)
- Dashboard with key metrics calculated from local data
- Room management (add, edit, delete rooms)
- Tenant management (add, edit, delete tenants)
- Rent payment tracking (mark rent as paid/unpaid)
- Data persistence using localStorage
- Profile management
- Responsive design with modern UI

## Tech Stack

- React with Vite
- Tailwind CSS for styling
- React Router for navigation
- React Icons for icons
- React Toastify for notifications
- localStorage for data persistence
- Mock API layer for realistic data operations

## Installation

1. Clone the repository
2. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```
3. Install dependencies:
   ```bash
   npm install
   ```

## Running the Application

Start the development server:

```bash
cd frontend
npm run dev
```

The application will be available at `http://localhost:5173`.

## Demo Credentials

Use these credentials to log in:

- **Email**: <EMAIL>
- **Password**: admin123

## Building for Production

To build the application for production:

```bash
cd frontend
npm run build
```

This will create a `dist` folder with the production-ready files.

## Data Storage

This application uses localStorage to persist data between sessions. The following data is stored:

- **Rooms**: Room information including capacity, rent amount, and amenities
- **Tenants**: Tenant details including personal information and room assignments
- **Rents**: Rent payment records and status
- **Admin**: Authentication token and admin profile data

## Resetting Data

To reset all data and start fresh:

1. Open browser developer tools (F12)
2. Go to Application/Storage tab
3. Clear localStorage for the site
4. Refresh the page

## Project Structure

```
frontend/
├── src/
│   ├── components/     # Reusable UI components
│   ├── pages/         # Page components for different routes
│   ├── context/       # React context providers
│   ├── utils/         # Utility functions and mock API
│   ├── styles/        # CSS files
│   └── App.jsx        # Main application component
├── public/            # Static assets
└── package.json       # Dependencies and scripts
```

## Future Enhancements

- Backend integration with real database
- User role management
- Email notifications for rent reminders
- Advanced analytics and reporting
- Mobile app integration
- Data export/import functionality
- Multi-language support
