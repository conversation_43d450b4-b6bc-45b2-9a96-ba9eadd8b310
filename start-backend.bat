@echo off
echo ========================================
echo    PG Management Backend Server
echo ========================================
echo.

echo 🔄 Killing existing Node processes...
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 >nul

echo 📁 Changing to backend directory...
cd backend
echo Current directory: %CD%
echo.

echo 🔧 Node version:
node --version
echo.

echo 🚀 Starting server...
echo ⏳ Please wait...
echo.
node server.js
echo.
echo ❌ Server stopped. Press any key to exit...
pause >nul
