# PG Hostel Management System - Frontend Only

A React-based frontend application for managing PG (Paying Guest) hostels. This application runs completely in the browser using mock data and localStorage for persistence.

## Features

- Mock admin authentication (demo: <EMAIL> / admin123)
- Dashboard with key metrics calculated from local data
- Room management (add, edit, delete rooms)
- Tenant management (add, edit, delete tenants)
- Rent payment tracking (mark rent as paid/unpaid)
- Profile management
- Data persistence using localStorage
- Responsive design with modern UI

## Tech Stack

- React with Vite
- React Router for navigation
- Tailwind CSS for styling
- React Icons for icons
- React Toastify for notifications
- localStorage for data persistence
- Mock API layer for realistic operations

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Start the development server:
   ```bash
   npm run dev
   ```

## Usage

1. Open the application in your browser (http://localhost:5173)
2. Login with demo credentials:
   - Email: <EMAIL>
   - Password: admin123
3. Explore the dashboard and manage rooms, tenants, and rent payments
4. All data is stored locally and persists between sessions

## Project Structure

- `src/components`: Reusable UI components
- `src/pages`: Page components for different routes
- `src/context`: React context providers for state management
- `src/utils`: Utility functions and mock API layer
- `src/styles`: CSS files for styling
- `src/App.jsx`: Main application component with routing
- `src/main.jsx`: Entry point for the application

## Mock API Layer

The application includes a comprehensive mock API layer (`src/utils/api.js`) that simulates backend operations:

- **Data Storage**: Uses localStorage for persistence
- **Realistic Delays**: Simulates network latency
- **CRUD Operations**: Full create, read, update, delete functionality
- **Data Relationships**: Maintains relationships between rooms, tenants, and rents
- **Error Handling**: Proper error responses and validation

## Deployment

To build the application for production:

```bash
npm run build
```

This will create a `dist` folder with the production-ready files that can be deployed to any static hosting service.
