/* Premium Navbar */
.premium-navbar {
  background: linear-gradient(165deg, #1e293b 0%, #0f172a 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 40;
  height: 4rem;
}

.premium-navbar::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at top right,
    rgba(99, 102, 241, 0.15),
    transparent 70%
  );
  z-index: 0;
}

.premium-navbar-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 1.5rem;
  height: 100%;
}

.premium-navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  position: relative;
}

/* Left side */
.premium-navbar-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.premium-navbar-menu-button {
  display: none;
  padding: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
  border-radius: 0.5rem;
  transition: all 0.2s;
  background: rgba(255, 255, 255, 0.1);
}

@media (max-width: 1024px) {
  .premium-navbar-menu-button {
    display: inline-flex;
  }
}

.premium-navbar-menu-button:hover {
  color: white;
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Search bar */
.premium-navbar-search-container {
  position: relative;
}

.premium-navbar-search {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.75rem;
  padding: 0.5rem 1rem;
  width: 300px;
  transition: all 0.2s;
}

.premium-navbar-search:hover,
.premium-navbar-search:focus-within {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.premium-search-icon {
  color: rgba(255, 255, 255, 0.5);
  margin-right: 0.75rem;
  font-size: 0.875rem;
}

.premium-search-input {
  background: transparent;
  border: none;
  outline: none;
  width: 100%;
  color: white;
  font-size: 0.875rem;
  padding: 0.25rem 0;
}

.premium-search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.premium-shortcuts-button {
  padding: 0.25rem;
  color: rgba(255, 255, 255, 0.5);
  border-radius: 0.375rem;
  transition: all 0.2s;
  margin-left: 0.5rem;
}

.premium-shortcuts-button:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

/* Shortcuts Dropdown */
.premium-shortcuts-dropdown {
  position: absolute;
  top: calc(100% + 0.5rem);
  left: 0;
  width: 400px;
  background: #1a1f38;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  z-index: 50;
  animation: dropdownFadeIn 0.2s ease-out;
}

.premium-shortcuts-header {
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.premium-shortcuts-header h3 {
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
}

.premium-shortcuts-list {
  max-height: 400px;
  overflow-y: auto;
}

.premium-shortcut-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.2s;
  border-left: 2px solid transparent;
}

.premium-shortcut-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-left-color: #6366f1;
}

.premium-shortcut-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  margin-right: 1rem;
}

.premium-shortcut-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.premium-shortcut-label {
  font-size: 0.875rem;
}

.premium-shortcut-key {
  padding: 0.25rem 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-family: monospace;
}

/* Quick Navigation */
.premium-quick-nav {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-right: 1.5rem;
}

.premium-quick-nav-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  color: rgba(255, 255, 255, 0.7);
  border-radius: 0.5rem;
  transition: all 0.2s;
  background: rgba(255, 255, 255, 0.1);
  position: relative;
}

.premium-quick-nav-item:hover {
  color: white;
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.premium-quick-nav-item::after {
  content: attr(title);
  position: absolute;
  bottom: -2.5rem;
  left: 50%;
  transform: translateX(-50%) scale(0.95);
  background: #1a1f38;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: all 0.2s;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.premium-quick-nav-item:hover::after {
  opacity: 1;
  transform: translateX(-50%) scale(1);
}

/* Right side */
.premium-navbar-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Notifications */
.premium-notifications {
  position: relative;
}

.premium-notification-button {
  position: relative;
  padding: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
  border-radius: 0.5rem;
  transition: all 0.2s;
  background: rgba(255, 255, 255, 0.1);
}

.premium-notification-button:hover {
  color: white;
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.premium-notification-badge {
  position: absolute;
  top: -0.25rem;
  right: -0.25rem;
  background: #ef4444;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #1e293b;
}

.premium-notifications-dropdown {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  width: 320px;
  background: #1a1f38;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  z-index: 50;
  animation: dropdownFadeIn 0.2s ease-out;
}

.premium-notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.premium-notifications-header h3 {
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
}

.premium-notifications-count {
  color: #6366f1;
  font-size: 0.75rem;
  font-weight: 500;
}

.premium-notifications-list {
  max-height: 360px;
  overflow-y: auto;
}

.premium-notification-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  gap: 1rem;
  transition: all 0.2s;
  border-left: 2px solid transparent;
}

.premium-notification-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-left-color: #6366f1;
}

.premium-notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
}

.premium-notification-content {
  flex: 1;
}

.premium-notification-text {
  color: white;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.premium-notification-time {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.75rem;
}

/* Profile Dropdown */
.premium-profile-dropdown {
  position: relative;
}

.premium-profile-button {
  display: flex;
  align-items: center;
  padding: 0.375rem;
  gap: 0.75rem;
  color: white;
  border-radius: 0.5rem;
  transition: all 0.2s;
  background: rgba(255, 255, 255, 0.1);
}

.premium-profile-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.premium-profile-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.premium-profile-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
}

.premium-profile-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
  margin-right: 0.5rem;
}

.premium-dropdown-menu {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  width: 280px;
  background: #1a1f38;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  z-index: 50;
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.premium-dropdown-header {
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.premium-dropdown-user {
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.premium-dropdown-email {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.75rem;
}

.premium-dropdown-pg-info {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.premium-dropdown-pg-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
}

.premium-dropdown-links {
  padding: 0.5rem;
}

.premium-dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  cursor: pointer;
  width: 100%;
  text-align: left;
}

.premium-dropdown-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.premium-dropdown-logout {
  color: #ef4444;
}

.premium-dropdown-logout:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* Responsive */
@media (max-width: 1024px) {
  .premium-navbar-search {
    width: 240px;
  }
}

@media (max-width: 640px) {
  .premium-navbar-search {
    display: none;
  }
  
  .premium-profile-name {
    display: none;
  }
}
