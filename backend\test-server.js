const express = require("express");
const cors = require("cors");

const app = express();

// Enable CORS
app.use(cors({
  origin: "*",
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"],
}));

app.use(express.json());

// Test route
app.get("/", (req, res) => {
  res.json({ message: "Test server is running!" });
});

// Test tenants route
app.get("/api/v1/tenants", (req, res) => {
  res.json({
    success: true,
    count: 2,
    data: [
      {
        _id: "test1",
        name: "Test Tenant 1",
        phone: "1234567890",
        email: "<EMAIL>",
        roomId: {
          _id: "room1",
          roomNumber: "101",
          floorNumber: 1
        },
        active: true,
        joiningDate: new Date().toISOString()
      },
      {
        _id: "test2",
        name: "Test Tenant 2",
        phone: "0987654321",
        email: "<EMAIL>",
        roomId: {
          _id: "room2",
          roomNumber: "102",
          floorNumber: 1
        },
        active: true,
        joiningDate: new Date().toISOString()
      }
    ]
  });
});

// Test last-updated route
app.get("/api/v1/tenants/last-updated", (req, res) => {
  res.json({
    success: true,
    lastUpdated: new Date().toISOString()
  });
});

const PORT = 5000;
app.listen(PORT, () => {
  console.log(`Test server running on port ${PORT}`);
});
