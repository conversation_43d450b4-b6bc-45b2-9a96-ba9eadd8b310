const axios = require('axios');

const API_BASE = 'http://localhost:5000/api/v1';

// Test data
const testAdmin = {
  name: 'Test Admin',
  email: '<EMAIL>',
  password: 'password123',
  pgName: 'Test PG',
  address: '123 Test Street',
  phone: '1234567890'
};

async function testAuth() {
  console.log('🧪 Testing Authentication Endpoints...\n');

  try {
    // Test 1: Register Admin
    console.log('1️⃣ Testing Admin Registration...');
    try {
      const registerResponse = await axios.post(`${API_BASE}/admin/register`, testAdmin);
      console.log('✅ Registration successful:', registerResponse.data.success);
      console.log('📧 Admin email:', registerResponse.data.admin.email);
      console.log('🎫 Token received:', !!registerResponse.data.token);
    } catch (error) {
      if (error.response?.data?.error?.includes('already exists')) {
        console.log('ℹ️  Admin already exists, proceeding to login test...');
      } else {
        console.error('❌ Registration failed:', error.response?.data?.error || error.message);
      }
    }

    console.log('\n2️⃣ Testing Admin Login...');
    try {
      const loginResponse = await axios.post(`${API_BASE}/admin/login`, {
        email: testAdmin.email,
        password: testAdmin.password
      });
      console.log('✅ Login successful:', loginResponse.data.success);
      console.log('📧 Admin email:', loginResponse.data.admin.email);
      console.log('🎫 Token received:', !!loginResponse.data.token);
      
      const token = loginResponse.data.token;

      // Test 3: Protected route
      console.log('\n3️⃣ Testing Protected Route (/admin/me)...');
      try {
        const meResponse = await axios.get(`${API_BASE}/admin/me`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        console.log('✅ Protected route access successful:', meResponse.data.success);
        console.log('👤 Admin name:', meResponse.data.data.name);
      } catch (error) {
        console.error('❌ Protected route failed:', error.response?.data?.error || error.message);
      }

    } catch (error) {
      console.error('❌ Login failed:', error.response?.data?.error || error.message);
    }

    // Test 4: Invalid login
    console.log('\n4️⃣ Testing Invalid Login...');
    try {
      await axios.post(`${API_BASE}/admin/login`, {
        email: testAdmin.email,
        password: 'wrongpassword'
      });
      console.log('❌ Invalid login should have failed!');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Invalid login correctly rejected');
      } else {
        console.error('❌ Unexpected error:', error.response?.data?.error || error.message);
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Check if server is running first
async function checkServer() {
  try {
    const response = await axios.get(`${API_BASE}/debug`);
    console.log('✅ Server is running');
    return true;
  } catch (error) {
    console.error('❌ Server is not running. Please start the server first.');
    console.log('💡 Run: node server.js');
    return false;
  }
}

async function main() {
  console.log('🔍 Checking server status...');
  const serverRunning = await checkServer();
  
  if (serverRunning) {
    await testAuth();
  }
  
  console.log('\n🏁 Test completed!');
}

main().catch(console.error);
