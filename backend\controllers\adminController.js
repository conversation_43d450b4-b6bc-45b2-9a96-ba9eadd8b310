const Admin = require('../models/Admin');
const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/async');

// @desc    Register admin
// @route   POST /api/v1/admin/register
// @access  Public
exports.registerAdmin = asyncHandler(async (req, res, next) => {
  const { name, email, password, pgName, address, phone } = req.body;

  // Log the request body to see what's being received
  console.log("📝 Register Admin Request Body:", req.body);

  // Validate required fields
  if (!name || !email || !password || !pgName || !address || !phone) {
    return next(new ErrorResponse('Please provide all required fields', 400));
  }

  // Check if admin already exists
  const existingAdmin = await Admin.findOne({ email });
  if (existingAdmin) {
    return next(new ErrorResponse('Admin with this email already exists', 400));
  }

  try {
    // Create admin
    const admin = await Admin.create({
      name,
      email,
      password,
      pgName,
      address,
      phone
    });

    console.log("✅ Admin created successfully:", admin.email);
    sendTokenResponse(admin, 201, res);
  } catch (error) {
    console.error("❌ Error during Admin creation:", error);

    // Handle specific MongoDB errors
    if (error.code === 11000) {
      const field = Object.keys(error.keyValue)[0];
      return next(new ErrorResponse(`Admin with this ${field} already exists`, 400));
    }

    if (error.name === 'ValidationError') {
      const message = Object.values(error.errors).map(val => val.message).join(', ');
      return next(new ErrorResponse(message, 400));
    }

    next(error);
  }
});

// @desc    Login admin
// @route   POST /api/v1/admin/login
// @access  Public
exports.loginAdmin = asyncHandler(async (req, res, next) => {
  const { email, password } = req.body;

  console.log("🔐 Login attempt for email:", email);

  // Validate email & password
  if (!email || !password) {
    return next(new ErrorResponse('Please provide an email and password', 400));
  }

  try {
    // Check for admin
    const admin = await Admin.findOne({ email }).select('+password');

    if (!admin) {
      console.log("❌ Admin not found for email:", email);
      return next(new ErrorResponse('Invalid credentials', 401));
    }

    console.log("👤 Admin found:", admin.name);

    // Check if password matches
    const isMatch = await admin.matchPassword(password);

    if (!isMatch) {
      console.log("❌ Password mismatch for admin:", admin.email);
      return next(new ErrorResponse('Invalid credentials', 401));
    }

    console.log("✅ Login successful for admin:", admin.email);
    sendTokenResponse(admin, 200, res);
  } catch (error) {
    console.error("❌ Login error:", error);
    return next(new ErrorResponse('Login failed. Please try again.', 500));
  }
});

// @desc    Get current logged in admin
// @route   GET /api/v1/admin/me
// @access  Private
exports.getMe = asyncHandler(async (req, res, next) => {
  const admin = await Admin.findById(req.admin.id);

  res.status(200).json({
    success: true,
    data: admin
  });
});

// @desc    Update admin details
// @route   PUT /api/v1/admin/updatedetails
// @access  Private
exports.updateDetails = asyncHandler(async (req, res, next) => {
  const fieldsToUpdate = {
    name: req.body.name,
    email: req.body.email,
    pgName: req.body.pgName,
    address: req.body.address,
    phone: req.body.phone
  };

  const admin = await Admin.findByIdAndUpdate(req.admin.id, fieldsToUpdate, {
    new: true,
    runValidators: true
  });

  res.status(200).json({
    success: true,
    data: admin
  });
});

// @desc    Update password
// @route   PUT /api/v1/admin/updatepassword
// @access  Private
exports.updatePassword = asyncHandler(async (req, res, next) => {
  const admin = await Admin.findById(req.admin.id).select('+password');

  // Check current password
  if (!(await admin.matchPassword(req.body.currentPassword))) {
    return next(new ErrorResponse('Password is incorrect', 401));
  }

  admin.password = req.body.newPassword;
  await admin.save();

  sendTokenResponse(admin, 200, res);
});

// Get token from model, create cookie and send response
const sendTokenResponse = (admin, statusCode, res) => {
  try {
    // Create token
    const token = admin.getSignedJwtToken();

    console.log("🎫 Token generated for admin:", admin.email);

    // Remove password from admin object before sending
    const adminResponse = {
      _id: admin._id,
      name: admin.name,
      email: admin.email,
      pgName: admin.pgName,
      address: admin.address,
      phone: admin.phone,
      createdAt: admin.createdAt
    };

    res.status(statusCode).json({
      success: true,
      token,
      admin: adminResponse
    });
  } catch (error) {
    console.error("❌ Error generating token response:", error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate authentication token'
    });
  }
};
