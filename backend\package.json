{"name": "pg-management-system-backend", "version": "1.0.0", "description": "Backend for PG Hostel Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["pg", "hostel", "management", "express", "mongodb", "mongoose"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "node-cron": "^4.1.0"}, "devDependencies": {"nodemon": "^3.0.1"}}