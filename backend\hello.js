console.log("Hello World!");
console.log("Node.js version:", process.version);
console.log("Platform:", process.platform);
console.log("Current working directory:", process.cwd());

// Test basic functionality
try {
  const fs = require('fs');
  console.log("✅ fs module loaded");
  
  const path = require('path');
  console.log("✅ path module loaded");
  
  const express = require('express');
  console.log("✅ express module loaded");
  
  console.log("✅ All basic modules working!");
  
} catch (error) {
  console.error("❌ Error loading modules:", error.message);
}

console.log("🎉 Test completed successfully!");
