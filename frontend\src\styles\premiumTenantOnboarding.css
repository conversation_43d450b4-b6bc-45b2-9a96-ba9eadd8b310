.premium-tenant-onboarding {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.premium-tenant-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3rem;
  position: relative;
}

.premium-tenant-steps::before {
  content: '';
  position: absolute;
  top: 24px;
  left: 0;
  right: 0;
  height: 2px;
  background: #e5e7eb;
  z-index: 0;
}

.premium-tenant-step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
  flex: 1;
}

.premium-tenant-step-number {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: white;
  border: 2px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  line-height: 48px;
  color: #6b7280;
  transition: all 0.3s ease;
}

.premium-tenant-step-indicator.active .premium-tenant-step-number {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.premium-tenant-step-title {
  font-size: 0.875rem;
  color: #6b7280;
  text-align: center;
  transition: all 0.3s ease;
}

.premium-tenant-step-indicator.active .premium-tenant-step-title {
  color: #3b82f6;
  font-weight: 500;
}

.premium-tenant-content {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  padding: 2rem;
  margin-bottom: 2rem;
}

.premium-tenant-step {
  max-width: 600px;
  margin: 0 auto;
}

.premium-tenant-review-item {
  background: #f9fafb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.premium-tenant-review-item img {
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.premium-tenant-back-btn {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  color: #4b5563;
  font-weight: 500;
  transition: all 0.2s ease;
}

.premium-tenant-back-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

@media (max-width: 768px) {
  .premium-tenant-steps {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
  }

  .premium-tenant-steps::before {
    display: none;
  }

  .premium-tenant-step-indicator {
    flex-direction: row;
    gap: 1rem;
    width: 100%;
  }

  .premium-tenant-step-number {
    margin-bottom: 0;
  }

  .premium-tenant-step-title {
    text-align: left;
  }
} 