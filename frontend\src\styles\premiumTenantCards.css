/* Premium Tenant Cards Styling */

.tenant-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 1.15rem;
}

.tenant-card {
  background: white;
  border-radius: 0.85rem;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05), 0 4px 8px rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 380px; /* Set a fixed height for uniformity */
  box-sizing: border-box; /* Include padding and border in element's total width and height */
  border: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 1;
  cursor: pointer; /* Add cursor pointer to indicate interactivity */
}

.tenant-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 25px rgba(0, 0, 0, 0.1), 0 8px 12px rgba(0, 0, 0, 0.04);
  z-index: 10; /* Ensure card is above others when hovered */
  transition: all 0.3s ease; /* Smooth transition */
}

/* Card header with gradient */
.tenant-card-header {
  padding: 0.85rem 1.1rem;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
}

.tenant-card-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.5;
  z-index: 0;
}

.tenant-card-header::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-25deg);
  animation: shimmer 3s infinite;
  z-index: 1;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 200%;
  }
}

.tenant-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  position: relative;
  z-index: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.01em;
}

/* Card body with tenant info */
.tenant-card-body {
  padding: 0.85rem;
  flex-grow: 1; /* Allow body to grow */
  background: linear-gradient(to bottom, #f9fafb, #f3f4f6);
  display: flex; /* Make body a flex container */
  flex-direction: column; /* Stack content vertically */
  gap: 0.6rem; /* Maintain gap for items within the body */
}

.tenant-card-info {
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
}

.tenant-card-info-item {
  display: flex;
  align-items: center;
  padding: 0.3rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.tenant-card-info-item:hover {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transform: translateX(3px);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.tenant-card-info-item:hover .tenant-card-info-icon {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(79, 70, 229, 0.4);
}

.tenant-card-info-item:hover .tenant-card-info-value {
  color: #4f46e5;
  font-weight: 700;
  transition: all 0.3s ease;
}

.tenant-card-info-icon {
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.4rem;
  color: white;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  border-radius: 0.4rem;
  box-shadow: 0 2px 5px rgba(79, 70, 229, 0.3);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.tenant-card-info-label {
  font-size: 0.7rem;
  color: #6b7280;
  width: 60px;
  font-weight: 500;
}

.tenant-card-info-value {
  font-size: 0.75rem;
  color: #1f2937;
  font-weight: 600;
  flex: 1;
  transition: all 0.3s ease;
}

/* Status badge */
.tenant-card-status {
  padding: 0.6rem 0.85rem;
  text-align: center;
  background: white;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.tenant-status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.3rem 0.85rem;
  border-radius: 9999px;
  font-weight: 600;
  font-size: 0.7rem;
  width: 100%;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.tenant-status-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.tenant-status-active {
  background: linear-gradient(to right, #10b981, #059669);
  color: white;
}

.tenant-status-inactive {
  background: linear-gradient(to right, #ef4444, #dc2626);
  color: white;
}

/* Card actions */
.tenant-card-actions {
  display: flex;
  justify-content: space-between;
  padding: 0.6rem 0.85rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  background: white;
  gap: 0.6rem;
  /* Add properties to control height if needed */
}

.tenant-card-btn {
  padding: 0.4rem 0.85rem;
  border-radius: 0.4rem;
  font-weight: 600;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  flex: 1;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border: none;
  cursor: pointer;
}

.tenant-card-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.tenant-card-btn:active {
  transform: translateY(-1px);
}

.tenant-card-btn-edit {
  background: linear-gradient(to right, #6366f1, #4f46e5);
  color: white;
}

.tenant-card-btn-edit:hover {
  background: linear-gradient(to right, #4f46e5, #4338ca);
}

.tenant-card-btn-delete {
  background: linear-gradient(to right, #ef4444, #dc2626);
  color: white;
}

.tenant-card-btn-delete:hover {
  background: linear-gradient(to right, #dc2626, #b91c1c);
}

.tenant-card-btn-icon {
  margin-right: 0.5rem;
}

/* Card animations */
@keyframes cardEntrance {
  from {
    opacity: 0;
    transform: translateY(25px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes cardPulse {
  0% {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05), 0 5px 10px rgba(0, 0, 0, 0.02);
    transform: translateY(0);
  }
  50% {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.08), 0 8px 15px rgba(0, 0, 0, 0.05);
    transform: translateY(-3px);
  }
  100% {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05), 0 5px 10px rgba(0, 0, 0, 0.02);
    transform: translateY(0);
  }
}

@keyframes iconSpin {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(90deg);
  }
  50% {
    transform: rotate(180deg);
  }
  75% {
    transform: rotate(270deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.tenant-card {
  animation: cardEntrance 0.5s ease-out forwards;
  opacity: 1; /* Change from 0 to 1 to ensure card is visible */
}

.tenant-card:hover .tenant-card-info-icon {
  animation: iconSpin 1s ease-in-out;
}

/* Staggered animation for multiple cards */
.tenant-cards-container .tenant-card:nth-child(1) {
  animation-delay: 0.1s;
}
.tenant-cards-container .tenant-card:nth-child(2) {
  animation-delay: 0.2s;
}
.tenant-cards-container .tenant-card:nth-child(3) {
  animation-delay: 0.3s;
}
.tenant-cards-container .tenant-card:nth-child(4) {
  animation-delay: 0.4s;
}
.tenant-cards-container .tenant-card:nth-child(5) {
  animation-delay: 0.5s;
}
.tenant-cards-container .tenant-card:nth-child(6) {
  animation-delay: 0.6s;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .tenant-cards-container {
    grid-template-columns: 1fr;
  }

  .tenant-card-header {
    padding: 1.25rem;
  }

  .tenant-card-body {
    padding: 1.25rem;
  }

  .tenant-card-actions {
    flex-direction: column;
  }
}
