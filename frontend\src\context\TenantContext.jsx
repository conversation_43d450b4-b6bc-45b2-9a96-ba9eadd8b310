import React, { createContext, useState, useContext, useEffect, useMemo, useCallback } from "react";
import { toast } from "react-toastify";
import api from "../utils/api";
import { useRooms } from "./RoomContext";
import { useLocation } from "react-router-dom";
import { showToast } from "../utils/toast";

// Create context
const TenantContext = createContext();

// Create a custom hook to use the tenant context
export const useTenants = () => useContext(TenantContext);

const AUTH_PAGES = ["/login", "/register", "/logout"];
function isAuthPage(location) {
  return AUTH_PAGES.includes(location.pathname);
}

export const TenantProvider = ({ children }) => {
  const [tenants, setTenants] = useState([]);
  const [loading, setLoading] = useState(true);
  const { rooms, fetchRooms } = useRooms();
  const location = useLocation();

  // Track last update time for polling
  const [lastUpdateTime, setLastUpdateTime] = useState(Date.now());
  const [initialized, setInitialized] = useState(false);

  const checkForUpdates = useCallback(async () => {
    try {
      const res = await api.get("/tenants/last-updated");
      if (res.data.success && res.data.lastUpdated) {
        if (new Date(res.data.lastUpdated) > new Date(lastUpdateTime)) {
          console.log("Tenant data updated in database, refreshing...");
          await fetchTenants();
          setLastUpdateTime(Date.now());
        }
      }
    } catch (err) {
      console.log("Error checking for tenant updates:", err);
      // Don't fail silently on network errors during polling
      if (err.code !== 'ERR_NETWORK') {
        console.error("Non-network error in polling:", err);
      }
    }
  }, [lastUpdateTime]);

  const fetchTenants = useCallback(async () => {
    const wasAlreadyInitialized = initialized;
    if (!wasAlreadyInitialized) {
      setLoading(true);
    }

    try {
      console.log("Fetching tenants...");
      const res = await api.get("/tenants");
      console.log("Tenants API response:", res.data);

      if (res.data.success && res.data.data) {
        if (!wasAlreadyInitialized) {
          console.log("Tenants fetched successfully:", res.data.data);
          if (res.data.data.length > 0) {
            const firstTenant = res.data.data[0];
            console.log(
              "First tenant roomId type:",
              typeof firstTenant.roomId,
              "Value:",
              firstTenant.roomId
            );
          }
        }

        // Ensure each tenant has the room data populated
        const tenantsWithRooms = res.data.data.map(tenant => {
          // Handle both populated and non-populated roomId
          let roomData = tenant.roomId;

          // If roomId is a string (ObjectId), find the room in the rooms array
          if (typeof tenant.roomId === 'string') {
            const room = rooms.find(r => r._id === tenant.roomId);
            roomData = room || tenant.roomId;
          }
          // If roomId is already an object, use it as is
          else if (typeof tenant.roomId === 'object' && tenant.roomId !== null) {
            roomData = tenant.roomId;
          }

          return {
            ...tenant,
            roomId: roomData,
            active: tenant.active ?? true,
            joiningDate: tenant.joiningDate ? new Date(tenant.joiningDate).toISOString() : new Date().toISOString()
          };
        });

        console.log("Setting tenants with rooms:", tenantsWithRooms);
        setTenants(tenantsWithRooms);
        setInitialized(true);
        setLastUpdateTime(Date.now());

        if (!wasAlreadyInitialized) {
          setLoading(false);
        }
        return tenantsWithRooms;
      } else {
        console.error("Failed to fetch tenants:", res.data);
        setTenants([]);
        if (!wasAlreadyInitialized) {
          setLoading(false);
        }
        return [];
      }
    } catch (err) {
      console.error("Error fetching tenants:", err);
      if (!isAuthPage(location)) {
        if (!(err.response && err.response.status === 401)) {
          if (!wasAlreadyInitialized) {
            showToast("Failed to fetch tenants", { type: "error" }, location);
          }
        }
      }
      setTenants([]);
      if (!wasAlreadyInitialized) {
        setLoading(false);
      }
      return [];
    }
  }, [initialized, location, rooms]);

  // Fetch tenants on component mount and set up polling
  useEffect(() => {
    const initializeData = async () => {
      try {
        console.log("Initializing tenant and room data...");
        // Ensure rooms are loaded first
        await fetchRooms();
        // Wait a bit to ensure rooms state is updated
        setTimeout(async () => {
          await fetchTenants();
        }, 100);
      } catch (error) {
        console.error("Error initializing data:", error);
      }
    };

    if (!isAuthPage(location)) {
      initializeData();
    }

    const pollingInterval = setInterval(() => {
      if (!isAuthPage(location)) {
        checkForUpdates();
      }
    }, 10000);

    return () => clearInterval(pollingInterval);
  }, [location.pathname, fetchRooms, fetchTenants, checkForUpdates]);

  const getTenantsByRoom = useCallback((roomId) => {
    return tenants.filter((tenant) => {
      if (typeof tenant.roomId === "object" && tenant.roomId !== null) {
        return tenant.roomId._id === roomId;
      }
      return tenant.roomId === roomId;
    });
  }, [tenants]);

  const addTenant = useCallback(async (tenantData) => {
    try {
      const room = rooms.find((r) => r._id === tenantData.roomId);
      if (!room) {
        showToast("Room not found", { type: "error" }, location);
        return false;
      }

      const roomTenants = getTenantsByRoom(tenantData.roomId);
      if (roomTenants.length >= room.capacity) {
        showToast(
          `Room ${room.roomNumber} on Floor ${room.floorNumber} is at full capacity`,
          { type: "error" },
          location
        );
        return false;
      }

      const res = await api.post("/tenants", tenantData);
      console.log("Add tenant response:", res.data);

      if (res.data.success) {
        const newTenant = {
          ...res.data.data,
          roomId: room,
          active: true,
          joiningDate: new Date(res.data.data.joiningDate).toISOString(),
        };
        console.log("Adding new tenant to state:", newTenant);
        setTenants(prevTenants => {
          const updatedTenants = [...prevTenants, newTenant];
          console.log("Updated tenants state:", updatedTenants);
          return updatedTenants;
        });
        await fetchRooms();
        showToast("Tenant added successfully", { type: "success" }, location);
        return true;
      }
      return false;
    } catch (err) {
      console.error("Error adding tenant:", err);
      showToast(
        err.response?.data?.error || "Failed to add tenant",
        { type: "error" },
        location
      );
      return false;
    }
  }, [rooms, getTenantsByRoom, fetchRooms, location]);

  const updateTenant = useCallback(async (id, tenantData) => {
    try {
      const res = await api.put(`/tenants/${id}`, tenantData);
      if (res.data.success) {
        setTenants((prevTenants) =>
          prevTenants.map((tenant) =>
            tenant._id === id ? { ...tenant, ...res.data.data } : tenant
          )
        );
        await fetchRooms();
        showToast("Tenant updated successfully", { type: "success" }, location);
        return true;
      }
      return false;
    } catch (err) {
      console.error("Error updating tenant:", err);
      showToast(
        err.response?.data?.error || "Failed to update tenant",
        { type: "error" },
        location
      );
      return false;
    }
  }, [fetchRooms, location]);

  const deleteTenant = useCallback(async (id) => {
    try {
      const res = await api.delete(`/tenants/${id}`);
      if (res.data.success) {
        setTenants((prevTenants) => prevTenants.filter((tenant) => tenant._id !== id));
        showToast("Tenant deleted successfully", { type: "success" }, location);
        return true;
      }
      return false;
    } catch (err) {
      console.error("Error deleting tenant:", err);
      showToast(
        err.response?.data?.error || "Failed to delete tenant",
        { type: "error" },
        location
      );
      return false;
    }
  }, [location]);

  const getTenant = useCallback((id) => {
    return tenants.find((tenant) => tenant._id === id);
  }, [tenants]);

  const getTenantRoomNumber = useCallback((roomId) => {
    const room = rooms.find((r) => r._id === roomId);
    return room ? `Room ${room.roomNumber} (Floor ${room.floorNumber})` : 'N/A';
  }, [rooms]);

  const value = useMemo(() => ({
        tenants,
        loading,
        initialized,
        fetchTenants,
        addTenant,
        updateTenant,
        deleteTenant,
        getTenant,
    getTenantsByRoom,
    getTenantRoomNumber,
  }), [tenants, loading, initialized, fetchTenants, addTenant, updateTenant, deleteTenant, getTenant, getTenantsByRoom, getTenantRoomNumber]);

  return (
    <TenantContext.Provider value={value}>
      {children}
    </TenantContext.Provider>
  );
};
