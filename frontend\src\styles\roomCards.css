/* Room Cards Styling */

.room-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.room-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.room-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.room-card-header {
  padding: 1.25rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: linear-gradient(135deg, #f0f9ff 0%, #e1f5fe 100%);
}

.room-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.room-card-subtitle {
  display: flex;
  justify-content: space-between;
  color: #4a5568;
  font-size: 0.875rem;
}

.room-card-body {
  padding: 1.25rem;
  flex-grow: 1;
}

.room-card-amenities {
  margin-bottom: 1.25rem;
}

.room-card-amenities-title {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.room-card-amenities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.room-card-amenity {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  color: #4a5568;
  display: flex;
  align-items: center;
}

.room-card-amenity-icon {
  margin-right: 0.25rem;
  color: #4299e1;
}

.room-card-status {
  margin-top: auto;
  padding: 0.75rem 1.25rem;
  text-align: center;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.375rem 1rem;
  border-radius: 9999px;
  font-weight: 600;
  font-size: 0.75rem;
  width: 100%;
  color: white !important;
}

.status-vacant {
  background: #22c55e !important;
  color: white !important;
}

.status-partial {
  background: #f59e0b !important;
  color: white !important;
}

.status-full {
  background: #ef4444 !important;
  color: white !important;
}

.room-card-actions {
  display: flex;
  justify-content: space-between;
  padding: 1rem 1.25rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  background: #f9fafb;
}

.room-card-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.room-card-btn-edit {
  background: linear-gradient(to right, #3182ce, #2b6cb0);
  color: white;
}

.room-card-btn-edit:hover {
  background: linear-gradient(to right, #2b6cb0, #2c5282);
}

.room-card-btn-delete {
  background: linear-gradient(to right, #f56565, #e53e3e);
  color: white;
}

.room-card-btn-delete:hover {
  background: linear-gradient(to right, #e53e3e, #c53030);
}

.room-card-btn-icon {
  margin-right: 0.375rem;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .room-cards-container {
    grid-template-columns: 1fr;
  }
}
