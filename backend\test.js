console.log("Node.js is working!");
console.log("Node version:", process.version);
console.log("Current directory:", process.cwd());

// Test requiring express
try {
  const express = require("express");
  console.log("✅ Express loaded successfully");
  
  const app = express();
  console.log("✅ Express app created");
  
  const PORT = 5000;
  const server = app.listen(PORT, () => {
    console.log(`✅ Server started on port ${PORT}`);
    console.log(`✅ Test URL: http://localhost:${PORT}`);
  });
  
  app.get("/", (req, res) => {
    res.send("Hello World!");
  });
  
} catch (error) {
  console.error("❌ Error:", error.message);
}
