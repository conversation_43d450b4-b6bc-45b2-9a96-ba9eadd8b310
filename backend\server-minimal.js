const express = require("express");
const dotenv = require("dotenv");
const cors = require("cors");

// Load env vars
dotenv.config();

console.log("🚀 Starting PG Management Server...");
console.log("📁 Current directory:", process.cwd());
console.log("🔧 Node version:", process.version);

const app = express();

// Body parser
app.use(express.json());

// Enable CORS
app.use(
  cors({
    origin: "*", // Allow all origins for now
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

// Handle OPTIONS preflight requests
app.options("*", (req, res) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  res.header("Access-Control-Allow-Headers", "Content-Type, Authorization");
  res.status(200).send();
});

// Add CORS headers to all responses
app.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  res.header("Access-Control-Allow-Headers", "Content-Type, Authorization");
  next();
});

// Mock data
const mockRooms = [
  {
    _id: "room1",
    roomNumber: "101",
    floorNumber: 1,
    capacity: 2,
    rentAmount: 5000,
    occupiedBeds: 1,
    isOccupied: false,
    adminId: "admin1",
    createdAt: new Date().toISOString()
  },
  {
    _id: "room2", 
    roomNumber: "102",
    floorNumber: 1,
    capacity: 2,
    rentAmount: 5500,
    occupiedBeds: 1,
    isOccupied: false,
    adminId: "admin1",
    createdAt: new Date().toISOString()
  }
];

const mockTenants = [
  {
    _id: "tenant1",
    name: "John Doe",
    phone: "1234567890",
    email: "<EMAIL>",
    roomId: {
      _id: "room1",
      roomNumber: "101",
      floorNumber: 1,
      capacity: 2,
      rentAmount: 5000
    },
    bedNumber: 1,
    active: true,
    joiningDate: new Date().toISOString(),
    adminId: "admin1",
    idProofType: "Aadhar",
    idProofNumber: "1234-5678-9012",
    occupation: "Software Engineer",
    paymentPeriod: "monthly",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    _id: "tenant2",
    name: "Jane Smith", 
    phone: "0987654321",
    email: "<EMAIL>",
    roomId: {
      _id: "room2",
      roomNumber: "102",
      floorNumber: 1,
      capacity: 2,
      rentAmount: 5500
    },
    bedNumber: 1,
    active: true,
    joiningDate: new Date().toISOString(),
    adminId: "admin1",
    idProofType: "PAN",
    idProofNumber: "**********",
    occupation: "Designer",
    paymentPeriod: "monthly",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// Basic route for testing
app.get("/", (req, res) => {
  console.log("📍 Root route accessed");
  res.json({ 
    message: "PG Management System API is running!",
    timestamp: new Date().toISOString(),
    status: "OK"
  });
});

// Debug route
app.get("/api/v1/debug", (req, res) => {
  console.log("🔍 Debug route accessed");
  res.json({
    environment: process.env.NODE_ENV || "development",
    nodeVersion: process.version,
    timestamp: new Date().toISOString(),
    headers: req.headers,
    cors: {
      origin: "*",
      methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
      allowedHeaders: ["Content-Type", "Authorization"],
    }
  });
});

// Mock rooms routes
app.get("/api/v1/rooms", (req, res) => {
  console.log("🏠 Rooms route accessed");
  res.json({
    success: true,
    count: mockRooms.length,
    data: mockRooms
  });
});

// Mock tenants routes
app.get("/api/v1/tenants", (req, res) => {
  console.log("👥 Tenants route accessed");
  res.json({
    success: true,
    count: mockTenants.length,
    data: mockTenants
  });
});

app.get("/api/v1/tenants/last-updated", (req, res) => {
  console.log("🕒 Last updated route accessed");
  res.json({
    success: true,
    lastUpdated: new Date().toISOString(),
    data: mockTenants[0]
  });
});

// Mock tenant creation
app.post("/api/v1/tenants", (req, res) => {
  console.log("➕ Create tenant route accessed", req.body);
  const newTenant = {
    _id: `tenant${Date.now()}`,
    ...req.body,
    active: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  // Find room and populate
  const room = mockRooms.find(r => r._id === req.body.roomId);
  if (room) {
    newTenant.roomId = room;
  }
  
  mockTenants.push(newTenant);
  
  res.status(201).json({
    success: true,
    data: newTenant
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error("❌ Error:", err);
  res.status(500).json({
    success: false,
    error: err.message || "Server Error"
  });
});

// 404 handler
app.use("*", (req, res) => {
  console.log("❓ 404 - Route not found:", req.originalUrl);
  res.status(404).json({
    success: false,
    error: `Route ${req.originalUrl} not found`
  });
});

const PORT = process.env.PORT || 5000;

const server = app.listen(PORT, () => {
  console.log("✅ Server running successfully!");
  console.log(`🌐 URL: http://localhost:${PORT}`);
  console.log(`🔗 API Base: http://localhost:${PORT}/api/v1`);
  console.log(`🧪 Test: http://localhost:${PORT}/api/v1/debug`);
  console.log("🎯 Ready to receive requests!");
});

// Handle server errors
server.on('error', (err) => {
  console.error("❌ Server error:", err);
  if (err.code === 'EADDRINUSE') {
    console.error(`❌ Port ${PORT} is already in use. Try a different port.`);
  }
});

// Handle unhandled promise rejections
process.on("unhandledRejection", (err, promise) => {
  console.error("❌ Unhandled Promise Rejection:", err.message);
});

// Handle uncaught exceptions
process.on("uncaughtException", (err) => {
  console.error("❌ Uncaught Exception:", err.message);
});

console.log("🔄 Server setup complete, waiting for connections...");
